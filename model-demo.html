<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汤山矿坑公园 - 3D模型导入演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #3498db;
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .feature-list {
            list-style: none;
            margin: 15px 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .code-block .comment {
            color: #95a5a6;
        }
        
        .code-block .keyword {
            color: #3498db;
        }
        
        .code-block .string {
            color: #2ecc71;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }
        
        .model-formats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .format-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: border-color 0.3s ease;
        }
        
        .format-card:hover {
            border-color: #3498db;
        }
        
        .format-card .format-name {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .format-card .format-desc {
            color: #666;
            font-size: 0.9rem;
        }
        
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .performance-table th,
        .performance-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .performance-table th {
            background: #3498db;
            color: white;
            font-weight: bold;
        }
        
        .performance-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-excellent { background: #27ae60; }
        .status-good { background: #f39c12; }
        .status-fair { background: #e67e22; }
        .status-poor { background: #e74c3c; }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .workflow-step {
            flex: 1;
            min-width: 200px;
            text-align: center;
            position: relative;
        }
        
        .workflow-step:not(:last-child):after {
            content: "→";
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            color: #3498db;
        }
        
        .step-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #3498db;
            color: white;
            border-radius: 50%;
            line-height: 40px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .step-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .step-desc {
            color: #666;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .workflow-steps {
                flex-direction: column;
            }
            
            .workflow-step:not(:last-child):after {
                content: "↓";
                right: auto;
                bottom: -15px;
                top: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🏗️ 3D模型导入系统</h1>
            <p>汤山矿坑公园三维导览系统 - 大型3D模型管理演示</p>
        </header>

        <div class="demo-section">
            <h2>🎯 系统特性</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">📦</div>
                    <h3>多格式支持</h3>
                    <p>支持主流3D模型格式，包括GLTF、FBX、OBJ等，满足不同建模软件的导出需求。</p>
                    <ul class="feature-list">
                        <li>GLTF/GLB (推荐)</li>
                        <li>FBX (Maya/3ds Max)</li>
                        <li>OBJ+MTL (通用格式)</li>
                        <li>DRACO压缩支持</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>性能优化</h3>
                    <p>智能的性能优化策略，确保大型模型在各种设备上流畅运行。</p>
                    <ul class="feature-list">
                        <li>自动LOD生成</li>
                        <li>实例化渲染</li>
                        <li>纹理压缩</li>
                        <li>几何体优化</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🎮</div>
                    <h3>智能管理</h3>
                    <p>完善的模型管理系统，支持分类加载、缓存管理和实时监控。</p>
                    <ul class="feature-list">
                        <li>分优先级加载</li>
                        <li>智能缓存机制</li>
                        <li>实时性能监控</li>
                        <li>错误恢复机制</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📁 支持的模型格式</h2>
            <div class="model-formats">
                <div class="format-card">
                    <div class="format-name">GLTF/GLB</div>
                    <div class="format-desc">推荐格式，支持PBR材质和动画</div>
                </div>
                <div class="format-card">
                    <div class="format-name">FBX</div>
                    <div class="format-desc">Autodesk格式，支持复杂动画</div>
                </div>
                <div class="format-card">
                    <div class="format-name">OBJ+MTL</div>
                    <div class="format-desc">通用格式，广泛支持</div>
                </div>
                <div class="format-card">
                    <div class="format-name">DRACO</div>
                    <div class="format-desc">Google压缩格式，减小文件大小</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 模型加载工作流</h2>
            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-title">模型配置</div>
                    <div class="step-desc">定义模型路径、位置、缩放等参数</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-title">分级加载</div>
                    <div class="step-desc">按优先级分批加载，确保核心内容优先</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-title">自动优化</div>
                    <div class="step-desc">几何体合并、材质优化、LOD生成</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-title">场景集成</div>
                    <div class="step-desc">添加到3D场景，设置光照和阴影</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📊 性能对比</h2>
            <table class="performance-table">
                <thead>
                    <tr>
                        <th>设备类型</th>
                        <th>最大三角形数</th>
                        <th>纹理质量</th>
                        <th>阴影效果</th>
                        <th>性能评级</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>高端桌面</td>
                        <td>1,000,000+</td>
                        <td>4K纹理</td>
                        <td>高质量阴影</td>
                        <td><span class="status-indicator status-excellent"></span>优秀</td>
                    </tr>
                    <tr>
                        <td>中端桌面</td>
                        <td>500,000</td>
                        <td>2K纹理</td>
                        <td>中等阴影</td>
                        <td><span class="status-indicator status-good"></span>良好</td>
                    </tr>
                    <tr>
                        <td>高端移动</td>
                        <td>200,000</td>
                        <td>1K纹理</td>
                        <td>简化阴影</td>
                        <td><span class="status-indicator status-fair"></span>一般</td>
                    </tr>
                    <tr>
                        <td>低端移动</td>
                        <td>50,000</td>
                        <td>512px纹理</td>
                        <td>无阴影</td>
                        <td><span class="status-indicator status-poor"></span>基础</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="demo-section">
            <h2>💻 代码示例</h2>
            <p>以下是如何使用模型管理器加载3D模型的示例代码：</p>
            
            <div class="code-block">
<span class="comment">// 初始化模型管理器</span>
<span class="keyword">const</span> modelManager = <span class="keyword">new</span> ModelManager(scene, renderer);

<span class="comment">// 加载GLTF模型</span>
<span class="keyword">const</span> model = <span class="keyword">await</span> modelManager.loadGLTFModel(
    <span class="string">'/assets/models/buildings/museum.glb'</span>,
    {
        scale: <span class="string">1.0</span>,
        position: { x: <span class="string">80</span>, y: <span class="string">10</span>, z: <span class="string">-20</span> },
        castShadow: <span class="keyword">true</span>,
        receiveShadow: <span class="keyword">true</span>
    }
);

<span class="comment">// 添加到场景</span>
scene.add(model);

<span class="comment">// 批量加载模型</span>
<span class="keyword">const</span> models = <span class="keyword">await</span> modelManager.loadModels([
    {
        type: <span class="string">'gltf'</span>,
        url: <span class="string">'/assets/models/terrain/landscape.glb'</span>,
        options: { scale: <span class="string">1.0</span> }
    },
    {
        type: <span class="string">'fbx'</span>,
        url: <span class="string">'/assets/models/vegetation/trees.fbx'</span>,
        options: { scale: <span class="string">0.01</span> }
    }
]);
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 开始使用</h2>
            <p>准备好体验强大的3D模型导入功能了吗？</p>
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.html" class="btn">启动主应用</a>
                <a href="test.html" class="btn btn-secondary">运行测试</a>
                <button class="btn" onclick="showModelManagement()">打开模型管理</button>
            </div>
        </div>
    </div>

    <script>
        function showModelManagement() {
            alert('模型管理功能演示\n\n在主应用中：\n• 点击导航栏的🏗️按钮\n• 或使用快捷键 Ctrl+M\n• 即可打开3D模型管理界面\n\n功能包括：\n✓ 实时性能监控\n✓ 模型加载进度\n✓ 分类显示控制\n✓ 性能级别调整');
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为特性卡片添加点击效果
            document.querySelectorAll('.feature-card').forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });

            // 为格式卡片添加悬停效果
            document.querySelectorAll('.format-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f8f9fa';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'white';
                });
            });
        });
    </script>
</body>
</html>
