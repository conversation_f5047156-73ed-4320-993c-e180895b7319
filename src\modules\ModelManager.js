// 3D模型管理器 - 处理大型3D模型的加载、优化和管理
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';

export class ModelManager {
    constructor(scene, renderer) {
        this.scene = scene;
        this.renderer = renderer;
        
        // 加载器实例
        this.loaders = {
            gltf: new GLTFLoader(),
            fbx: new FBXLoader(),
            obj: new OBJLoader(),
            mtl: new MTLLoader(),
            draco: new DRACOLoader(),
            ktx2: new KTX2Loader()
        };
        
        // 模型缓存
        this.modelCache = new Map();
        this.loadingPromises = new Map();
        
        // 性能配置
        this.performanceConfig = {
            maxTriangles: 500000,
            maxTextures: 50,
            maxMaterials: 100,
            enableLOD: true,
            enableInstancing: true,
            enableOcclusion: true
        };
        
        // 加载进度回调
        this.onProgress = null;
        this.onError = null;
        
        this.initializeLoaders();
    }

    initializeLoaders() {
        // 配置DRACO压缩解码器
        this.loaders.draco.setDecoderPath('/libs/draco/');
        this.loaders.gltf.setDRACOLoader(this.loaders.draco);
        
        // 配置KTX2纹理加载器
        this.loaders.ktx2.setTranscoderPath('/libs/basis/');
        this.loaders.gltf.setKTX2Loader(this.loaders.ktx2);
        
        // 设置MTL加载器路径
        this.loaders.mtl.setPath('/assets/models/materials/');
        
        console.log('3D模型加载器初始化完成');
    }

    // 加载GLTF/GLB模型（推荐格式）
    async loadGLTFModel(url, options = {}) {
        const cacheKey = `gltf_${url}`;
        
        // 检查缓存
        if (this.modelCache.has(cacheKey)) {
            return this.cloneModel(this.modelCache.get(cacheKey));
        }
        
        // 检查是否正在加载
        if (this.loadingPromises.has(cacheKey)) {
            return this.loadingPromises.get(cacheKey);
        }
        
        const loadPromise = new Promise((resolve, reject) => {
            this.loaders.gltf.load(
                url,
                (gltf) => {
                    const model = this.processGLTFModel(gltf, options);
                    this.modelCache.set(cacheKey, model);
                    this.loadingPromises.delete(cacheKey);
                    resolve(this.cloneModel(model));
                },
                (progress) => {
                    if (this.onProgress) {
                        this.onProgress(progress, url);
                    }
                },
                (error) => {
                    this.loadingPromises.delete(cacheKey);
                    if (this.onError) {
                        this.onError(error, url);
                    }
                    reject(error);
                }
            );
        });
        
        this.loadingPromises.set(cacheKey, loadPromise);
        return loadPromise;
    }

    // 处理GLTF模型
    processGLTFModel(gltf, options) {
        const model = gltf.scene;
        
        // 设置模型属性
        if (options.scale) {
            model.scale.setScalar(options.scale);
        }
        
        if (options.position) {
            model.position.copy(options.position);
        }
        
        if (options.rotation) {
            model.rotation.copy(options.rotation);
        }
        
        // 优化模型
        this.optimizeModel(model, options);
        
        // 处理动画
        if (gltf.animations && gltf.animations.length > 0) {
            model.userData.animations = gltf.animations;
            model.userData.mixer = new THREE.AnimationMixer(model);
        }
        
        // 处理材质
        this.processMaterials(model, options);
        
        return model;
    }

    // 加载FBX模型
    async loadFBXModel(url, options = {}) {
        const cacheKey = `fbx_${url}`;
        
        if (this.modelCache.has(cacheKey)) {
            return this.cloneModel(this.modelCache.get(cacheKey));
        }
        
        if (this.loadingPromises.has(cacheKey)) {
            return this.loadingPromises.get(cacheKey);
        }
        
        const loadPromise = new Promise((resolve, reject) => {
            this.loaders.fbx.load(
                url,
                (fbx) => {
                    const model = this.processFBXModel(fbx, options);
                    this.modelCache.set(cacheKey, model);
                    this.loadingPromises.delete(cacheKey);
                    resolve(this.cloneModel(model));
                },
                (progress) => {
                    if (this.onProgress) {
                        this.onProgress(progress, url);
                    }
                },
                (error) => {
                    this.loadingPromises.delete(cacheKey);
                    if (this.onError) {
                        this.onError(error, url);
                    }
                    reject(error);
                }
            );
        });
        
        this.loadingPromises.set(cacheKey, loadPromise);
        return loadPromise;
    }

    // 处理FBX模型
    processFBXModel(fbx, options) {
        // FBX模型通常需要缩放
        if (!options.scale) {
            fbx.scale.setScalar(0.01); // FBX通常以厘米为单位
        } else {
            fbx.scale.setScalar(options.scale);
        }
        
        if (options.position) {
            fbx.position.copy(options.position);
        }
        
        if (options.rotation) {
            fbx.rotation.copy(options.rotation);
        }
        
        // 处理动画
        if (fbx.animations && fbx.animations.length > 0) {
            fbx.userData.animations = fbx.animations;
            fbx.userData.mixer = new THREE.AnimationMixer(fbx);
        }
        
        // 优化模型
        this.optimizeModel(fbx, options);
        
        return fbx;
    }

    // 加载OBJ模型（带MTL材质）
    async loadOBJModel(objUrl, mtlUrl = null, options = {}) {
        const cacheKey = `obj_${objUrl}_${mtlUrl}`;
        
        if (this.modelCache.has(cacheKey)) {
            return this.cloneModel(this.modelCache.get(cacheKey));
        }
        
        if (this.loadingPromises.has(cacheKey)) {
            return this.loadingPromises.get(cacheKey);
        }
        
        const loadPromise = new Promise((resolve, reject) => {
            if (mtlUrl) {
                // 先加载材质文件
                this.loaders.mtl.load(
                    mtlUrl,
                    (materials) => {
                        materials.preload();
                        this.loaders.obj.setMaterials(materials);
                        
                        // 然后加载OBJ模型
                        this.loaders.obj.load(
                            objUrl,
                            (obj) => {
                                const model = this.processOBJModel(obj, options);
                                this.modelCache.set(cacheKey, model);
                                this.loadingPromises.delete(cacheKey);
                                resolve(this.cloneModel(model));
                            },
                            (progress) => {
                                if (this.onProgress) {
                                    this.onProgress(progress, objUrl);
                                }
                            },
                            reject
                        );
                    },
                    undefined,
                    reject
                );
            } else {
                // 直接加载OBJ模型
                this.loaders.obj.load(
                    objUrl,
                    (obj) => {
                        const model = this.processOBJModel(obj, options);
                        this.modelCache.set(cacheKey, model);
                        this.loadingPromises.delete(cacheKey);
                        resolve(this.cloneModel(model));
                    },
                    (progress) => {
                        if (this.onProgress) {
                            this.onProgress(progress, objUrl);
                        }
                    },
                    reject
                );
            }
        });
        
        this.loadingPromises.set(cacheKey, loadPromise);
        return loadPromise;
    }

    // 处理OBJ模型
    processOBJModel(obj, options) {
        if (options.scale) {
            obj.scale.setScalar(options.scale);
        }
        
        if (options.position) {
            obj.position.copy(options.position);
        }
        
        if (options.rotation) {
            obj.rotation.copy(options.rotation);
        }
        
        // 优化模型
        this.optimizeModel(obj, options);
        
        return obj;
    }

    // 模型优化
    optimizeModel(model, options = {}) {
        let triangleCount = 0;
        let materialCount = 0;
        
        model.traverse((child) => {
            if (child.isMesh) {
                // 启用阴影
                if (options.castShadow !== false) {
                    child.castShadow = true;
                }
                if (options.receiveShadow !== false) {
                    child.receiveShadow = true;
                }
                
                // 计算三角形数量
                if (child.geometry) {
                    const positions = child.geometry.attributes.position;
                    if (positions) {
                        triangleCount += positions.count / 3;
                    }
                    
                    // 优化几何体
                    this.optimizeGeometry(child.geometry);
                }
                
                // 优化材质
                if (child.material) {
                    materialCount++;
                    this.optimizeMaterial(child.material, options);
                }
            }
        });
        
        console.log(`模型优化完成: ${triangleCount.toFixed(0)} 三角形, ${materialCount} 材质`);
        
        // 如果三角形数量过多，创建LOD
        if (triangleCount > this.performanceConfig.maxTriangles && this.performanceConfig.enableLOD) {
            this.createLOD(model, options);
        }
        
        return model;
    }

    // 几何体优化
    optimizeGeometry(geometry) {
        // 合并顶点
        geometry.mergeVertices();
        
        // 计算法线
        if (!geometry.attributes.normal) {
            geometry.computeVertexNormals();
        }
        
        // 计算边界球
        geometry.computeBoundingSphere();
        
        // 计算边界盒
        geometry.computeBoundingBox();
    }

    // 材质优化
    optimizeMaterial(material, options) {
        // 设置材质属性
        if (material.isMeshStandardMaterial || material.isMeshPhysicalMaterial) {
            // 启用环境遮蔽
            if (options.enableAO !== false) {
                material.aoMapIntensity = 1.0;
            }
            
            // 设置金属度和粗糙度
            if (options.metalness !== undefined) {
                material.metalness = options.metalness;
            }
            if (options.roughness !== undefined) {
                material.roughness = options.roughness;
            }
        }
        
        // 纹理优化
        if (material.map) {
            this.optimizeTexture(material.map);
        }
        if (material.normalMap) {
            this.optimizeTexture(material.normalMap);
        }
        if (material.roughnessMap) {
            this.optimizeTexture(material.roughnessMap);
        }
        if (material.metalnessMap) {
            this.optimizeTexture(material.metalnessMap);
        }
    }

    // 纹理优化
    optimizeTexture(texture) {
        // 设置纹理过滤
        texture.minFilter = THREE.LinearMipmapLinearFilter;
        texture.magFilter = THREE.LinearFilter;
        
        // 启用各向异性过滤
        texture.anisotropy = this.renderer.capabilities.getMaxAnisotropy();
        
        // 设置纹理包装
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
    }

    // 创建LOD (Level of Detail)
    createLOD(model, options) {
        const lod = new THREE.LOD();
        
        // 原始模型 (近距离)
        lod.addLevel(model, 0);
        
        // 中等细节模型 (中距离)
        const mediumModel = this.createSimplifiedModel(model, 0.5);
        lod.addLevel(mediumModel, 50);
        
        // 低细节模型 (远距离)
        const lowModel = this.createSimplifiedModel(model, 0.2);
        lod.addLevel(lowModel, 100);
        
        return lod;
    }

    // 创建简化模型
    createSimplifiedModel(model, simplificationRatio) {
        const simplified = model.clone();
        
        simplified.traverse((child) => {
            if (child.isMesh && child.geometry) {
                // 这里可以集成SimplifyModifier或其他简化算法
                // 目前使用简单的面数减少
                const geometry = child.geometry;
                const positions = geometry.attributes.position.array;
                const newPositions = new Float32Array(Math.floor(positions.length * simplificationRatio));
                
                for (let i = 0; i < newPositions.length; i++) {
                    newPositions[i] = positions[i];
                }
                
                const newGeometry = new THREE.BufferGeometry();
                newGeometry.setAttribute('position', new THREE.BufferAttribute(newPositions, 3));
                newGeometry.computeVertexNormals();
                
                child.geometry = newGeometry;
            }
        });
        
        return simplified;
    }

    // 克隆模型
    cloneModel(model) {
        const cloned = model.clone();
        
        // 克隆动画混合器
        if (model.userData.animations) {
            cloned.userData.animations = model.userData.animations;
            cloned.userData.mixer = new THREE.AnimationMixer(cloned);
        }
        
        return cloned;
    }

    // 处理材质
    processMaterials(model, options) {
        const materials = new Set();
        
        model.traverse((child) => {
            if (child.isMesh && child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => materials.add(mat));
                } else {
                    materials.add(child.material);
                }
            }
        });
        
        // 应用全局材质设置
        materials.forEach(material => {
            if (options.wireframe) {
                material.wireframe = true;
            }
            
            if (options.transparent) {
                material.transparent = true;
                material.opacity = options.opacity || 0.8;
            }
            
            if (options.color) {
                material.color = new THREE.Color(options.color);
            }
        });
    }

    // 批量加载模型
    async loadModels(modelConfigs) {
        const loadPromises = modelConfigs.map(config => {
            switch (config.type) {
                case 'gltf':
                case 'glb':
                    return this.loadGLTFModel(config.url, config.options);
                case 'fbx':
                    return this.loadFBXModel(config.url, config.options);
                case 'obj':
                    return this.loadOBJModel(config.url, config.mtlUrl, config.options);
                default:
                    throw new Error(`不支持的模型格式: ${config.type}`);
            }
        });
        
        try {
            const models = await Promise.all(loadPromises);
            return models;
        } catch (error) {
            console.error('批量加载模型失败:', error);
            throw error;
        }
    }

    // 获取模型信息
    getModelInfo(model) {
        let triangleCount = 0;
        let vertexCount = 0;
        let materialCount = 0;
        const materials = new Set();
        
        model.traverse((child) => {
            if (child.isMesh) {
                if (child.geometry) {
                    const positions = child.geometry.attributes.position;
                    if (positions) {
                        vertexCount += positions.count;
                        triangleCount += positions.count / 3;
                    }
                }
                
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => materials.add(mat));
                    } else {
                        materials.add(child.material);
                    }
                }
            }
        });
        
        materialCount = materials.size;
        
        return {
            triangles: Math.floor(triangleCount),
            vertices: vertexCount,
            materials: materialCount,
            boundingBox: model.userData.boundingBox || null,
            animations: model.userData.animations ? model.userData.animations.length : 0
        };
    }

    // 清理缓存
    clearCache() {
        this.modelCache.clear();
        this.loadingPromises.clear();
        console.log('模型缓存已清理');
    }

    // 设置性能配置
    setPerformanceConfig(config) {
        this.performanceConfig = { ...this.performanceConfig, ...config };
    }

    // 设置回调函数
    setCallbacks(onProgress, onError) {
        this.onProgress = onProgress;
        this.onError = onError;
    }

    // 销毁资源
    dispose() {
        this.clearCache();
        
        // 销毁加载器
        Object.values(this.loaders).forEach(loader => {
            if (loader.dispose) {
                loader.dispose();
            }
        });
        
        console.log('模型管理器已销毁');
    }
}
