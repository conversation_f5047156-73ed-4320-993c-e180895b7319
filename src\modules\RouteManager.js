// 路线管理器 - 处理游览路线规划和导航
import * as THREE from 'three';

export class RouteManager {
    constructor(sceneManager, dataManager) {
        this.sceneManager = sceneManager;
        this.dataManager = dataManager;
        
        this.currentRoute = null;
        this.routePath = null;
        this.routeMarkers = [];
        this.routeProgress = 0;
        this.isNavigating = false;
        
        // UI元素
        this.routeContainer = document.getElementById('route-container');
        this.routeSidebar = document.querySelector('.route-sidebar');
        this.route3DView = document.getElementById('route-3d-view');
        this.poiCheckboxes = document.getElementById('poi-checkboxes');
        
        // 路线渲染器
        this.routeRenderer = null;
        this.routeScene = null;
        this.routeCamera = null;
        
        this.isInitialized = false;
    }

    async init() {
        try {
            this.setupRouteUI();
            this.setup3DRouteView();
            this.bindEvents();
            this.loadRouteOptions();
            
            this.isInitialized = true;
            console.log('路线管理器初始化完成');
        } catch (error) {
            console.error('路线管理器初始化失败:', error);
            throw error;
        }
    }

    setupRouteUI() {
        // 设置POI选择列表
        const pois = this.dataManager.getAllPOIs();
        this.poiCheckboxes.innerHTML = '';
        
        pois.forEach(poi => {
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'poi-checkbox-item';
            checkboxContainer.innerHTML = `
                <label>
                    <input type="checkbox" value="${poi.id}" data-poi-id="${poi.id}">
                    <span class="poi-name">${poi.name}</span>
                    <span class="poi-duration">(${poi.visitDuration}分钟)</span>
                </label>
            `;
            this.poiCheckboxes.appendChild(checkboxContainer);
        });
    }

    setup3DRouteView() {
        // 创建路线专用的3D视图
        this.routeRenderer = new THREE.WebGLRenderer({ antialias: true });
        this.routeRenderer.setSize(this.route3DView.clientWidth, this.route3DView.clientHeight);
        this.routeRenderer.setClearColor(0xf0f0f0);
        this.route3DView.appendChild(this.routeRenderer.domElement);
        
        // 创建场景和相机
        this.routeScene = new THREE.Scene();
        this.routeCamera = new THREE.PerspectiveCamera(
            75,
            this.route3DView.clientWidth / this.route3DView.clientHeight,
            0.1,
            1000
        );
        
        // 添加光照
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.routeScene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        this.routeScene.add(directionalLight);
        
        // 设置相机位置
        this.routeCamera.position.set(0, 100, 100);
        this.routeCamera.lookAt(0, 0, 0);
    }

    bindEvents() {
        // 路线类型选择
        document.querySelectorAll('input[name="route-type"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.onRouteTypeChanged(e.target.value);
            });
        });

        // 生成路线按钮
        document.getElementById('generate-route').addEventListener('click', () => {
            this.generateRoute();
        });

        // POI复选框变化
        this.poiCheckboxes.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox') {
                this.onPOISelectionChanged();
            }
        });

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleRouteViewResize();
        });

        // 全局事件监听
        window.addEventListener('planRouteToAR', (e) => {
            this.planRouteTo(e.detail.poiId);
        });

        window.addEventListener('poiClicked', (e) => {
            this.highlightPOIInRoute(e.detail.poiId);
        });
    }

    loadRouteOptions() {
        // 加载预设路线选项
        const routes = this.dataManager.getAllRoutes();
        
        // 默认选择推荐路线
        const recommendedRoute = routes.find(r => r.id === 'route_recommended');
        if (recommendedRoute) {
            this.selectRoute(recommendedRoute);
        }
    }

    onRouteTypeChanged(routeType) {
        const routes = this.dataManager.getAllRoutes();
        
        if (routeType === 'custom') {
            // 启用自定义选择
            this.enableCustomSelection();
        } else {
            // 选择预设路线
            const route = routes.find(r => r.id === `route_${routeType}`);
            if (route) {
                this.selectRoute(route);
                this.updatePOISelection(route.pois);
            }
        }
    }

    enableCustomSelection() {
        // 清除所有选择
        this.poiCheckboxes.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
            checkbox.disabled = false;
        });
        
        this.currentRoute = null;
        this.clearRouteVisualization();
    }

    updatePOISelection(poiIds) {
        // 更新POI选择状态
        this.poiCheckboxes.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            const poiId = checkbox.dataset.poiId;
            checkbox.checked = poiIds.includes(poiId);
            checkbox.disabled = true; // 预设路线不允许修改
        });
    }

    onPOISelectionChanged() {
        // 自定义路线时，实时更新路线预览
        const selectedPOIs = this.getSelectedPOIs();
        if (selectedPOIs.length >= 2) {
            this.generateCustomRoute(selectedPOIs);
        } else {
            this.clearRouteVisualization();
        }
    }

    getSelectedPOIs() {
        const selected = [];
        this.poiCheckboxes.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
            selected.push(checkbox.dataset.poiId);
        });
        return selected;
    }

    selectRoute(route) {
        this.currentRoute = route;
        this.visualizeRoute(route);
        this.updateRouteInfo(route);
    }

    generateRoute() {
        const routeType = document.querySelector('input[name="route-type"]:checked').value;
        
        if (routeType === 'custom') {
            const selectedPOIs = this.getSelectedPOIs();
            if (selectedPOIs.length < 2) {
                this.showRouteNotification('请至少选择2个景点', 'warning');
                return;
            }
            this.generateCustomRoute(selectedPOIs);
        } else {
            const routes = this.dataManager.getAllRoutes();
            const route = routes.find(r => r.id === `route_${routeType}`);
            if (route) {
                this.selectRoute(route);
                this.showRouteNotification(`已生成${route.name}`, 'success');
            }
        }
    }

    generateCustomRoute(poiIds) {
        // 生成自定义路线
        const pois = poiIds.map(id => this.dataManager.getPOI(id)).filter(poi => poi);
        
        if (pois.length < 2) return;
        
        // 计算最优路径（简单的最近邻算法）
        const optimizedPath = this.optimizeRoute(pois);
        
        // 创建自定义路线对象
        const customRoute = {
            id: 'route_custom',
            name: '自定义路线',
            description: '用户自定义的游览路线',
            duration: this.calculateRouteDuration(optimizedPath),
            difficulty: 'medium',
            distance: this.calculateRouteDistance(optimizedPath),
            pois: optimizedPath.map(poi => poi.id),
            path: optimizedPath.map(poi => poi.position),
            highlights: optimizedPath.map(poi => poi.name),
            tips: '请根据实际情况调整游览时间'
        };
        
        this.selectRoute(customRoute);
        this.showRouteNotification('自定义路线已生成', 'success');
    }

    optimizeRoute(pois) {
        // 简单的最近邻路径优化
        if (pois.length <= 2) return pois;
        
        const optimized = [pois[0]]; // 从第一个POI开始
        const remaining = pois.slice(1);
        
        while (remaining.length > 0) {
            const current = optimized[optimized.length - 1];
            let nearest = remaining[0];
            let minDistance = this.calculateDistance(current.position, nearest.position);
            let nearestIndex = 0;
            
            for (let i = 1; i < remaining.length; i++) {
                const distance = this.calculateDistance(current.position, remaining[i].position);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = remaining[i];
                    nearestIndex = i;
                }
            }
            
            optimized.push(nearest);
            remaining.splice(nearestIndex, 1);
        }
        
        return optimized;
    }

    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    calculateRouteDuration(pois) {
        // 计算路线总时长
        let totalDuration = 0;
        pois.forEach(poi => {
            totalDuration += poi.visitDuration;
        });
        // 加上行走时间（估算）
        totalDuration += (pois.length - 1) * 10;
        return totalDuration;
    }

    calculateRouteDistance(pois) {
        // 计算路线总距离
        let totalDistance = 0;
        for (let i = 0; i < pois.length - 1; i++) {
            totalDistance += this.calculateDistance(pois[i].position, pois[i + 1].position);
        }
        return totalDistance / 50; // 转换为公里（假设单位）
    }

    visualizeRoute(route) {
        this.clearRouteVisualization();
        
        if (!route || !route.path) return;
        
        // 在3D视图中绘制路线
        this.drawRoutePath(route.path);
        this.drawRoutePOIs(route.pois);
        
        // 在主场景中也显示路线（如果当前在3D模式）
        if (this.sceneManager && this.sceneManager.scene) {
            this.drawRouteInMainScene(route);
        }
        
        this.renderRouteView();
    }

    drawRoutePath(pathPoints) {
        // 创建路径线条
        const points = pathPoints.map(p => new THREE.Vector3(p.x, p.y + 2, p.z));
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({ 
            color: 0xff4444, 
            linewidth: 3 
        });
        
        this.routePath = new THREE.Line(geometry, material);
        this.routeScene.add(this.routePath);
        
        // 添加路径动画
        this.animateRoutePath();
    }

    drawRoutePOIs(poiIds) {
        // 在路线视图中显示POI标记
        poiIds.forEach((poiId, index) => {
            const poi = this.dataManager.getPOI(poiId);
            if (!poi) return;
            
            // 创建POI标记
            const markerGeometry = new THREE.SphereGeometry(2, 16, 16);
            const markerMaterial = new THREE.MeshBasicMaterial({ 
                color: index === 0 ? 0x00ff00 : (index === poiIds.length - 1 ? 0xff0000 : 0x0088ff)
            });
            
            const marker = new THREE.Mesh(markerGeometry, markerMaterial);
            marker.position.set(poi.position.x, poi.position.y + 2, poi.position.z);
            marker.userData = { poiId: poi.id, index: index };
            
            this.routeScene.add(marker);
            this.routeMarkers.push(marker);
            
            // 添加文字标签
            this.addPOILabel(poi, marker.position);
        });
    }

    addPOILabel(poi, position) {
        // 创建文字标签（简化版本）
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        context.fillStyle = 'rgba(255, 255, 255, 0.8)';
        context.fillRect(0, 0, canvas.width, canvas.height);
        context.fillStyle = 'black';
        context.font = '20px Arial';
        context.textAlign = 'center';
        context.fillText(poi.name, canvas.width / 2, canvas.height / 2 + 7);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.position.set(position.x, position.y + 5, position.z);
        sprite.scale.set(10, 2.5, 1);
        
        this.routeScene.add(sprite);
        this.routeMarkers.push(sprite);
    }

    drawRouteInMainScene(route) {
        // 在主3D场景中显示路线
        if (!this.sceneManager.scene) return;
        
        // 移除之前的路线
        this.clearMainSceneRoute();
        
        // 绘制路径
        const points = route.path.map(p => new THREE.Vector3(p.x, p.y + 1, p.z));
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({ 
            color: 0xff4444, 
            linewidth: 2 
        });
        
        const routeLine = new THREE.Line(geometry, material);
        routeLine.userData = { isRouteLine: true };
        this.sceneManager.scene.add(routeLine);
    }

    clearMainSceneRoute() {
        if (!this.sceneManager.scene) return;
        
        const toRemove = [];
        this.sceneManager.scene.traverse(child => {
            if (child.userData && child.userData.isRouteLine) {
                toRemove.push(child);
            }
        });
        
        toRemove.forEach(obj => {
            this.sceneManager.scene.remove(obj);
        });
    }

    animateRoutePath() {
        // 路径动画效果
        if (!this.routePath) return;
        
        const material = this.routePath.material;
        let opacity = 0.5;
        let direction = 1;
        
        const animate = () => {
            opacity += direction * 0.02;
            if (opacity >= 1) {
                opacity = 1;
                direction = -1;
            } else if (opacity <= 0.5) {
                opacity = 0.5;
                direction = 1;
            }
            
            material.opacity = opacity;
            material.transparent = true;
            
            if (this.currentRoute) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }

    clearRouteVisualization() {
        // 清除路线可视化
        if (this.routePath) {
            this.routeScene.remove(this.routePath);
            this.routePath = null;
        }
        
        this.routeMarkers.forEach(marker => {
            this.routeScene.remove(marker);
        });
        this.routeMarkers = [];
        
        this.clearMainSceneRoute();
        this.renderRouteView();
    }

    updateRouteInfo(route) {
        // 更新路线信息显示
        const infoHTML = `
            <div class="route-info">
                <h4>${route.name}</h4>
                <p>${route.description}</p>
                <div class="route-stats">
                    <span>⏱️ ${route.duration}分钟</span>
                    <span>📏 ${route.distance.toFixed(1)}公里</span>
                    <span>🏃 ${route.difficulty}</span>
                </div>
                <div class="route-highlights">
                    <strong>亮点：</strong>${route.highlights.join('、')}
                </div>
                <div class="route-tips">
                    <strong>提示：</strong>${route.tips}
                </div>
            </div>
        `;
        
        // 如果有路线信息容器，更新它
        let infoContainer = this.routeSidebar.querySelector('.route-info');
        if (!infoContainer) {
            infoContainer = document.createElement('div');
            infoContainer.className = 'route-info-container';
            this.routeSidebar.appendChild(infoContainer);
        }
        infoContainer.innerHTML = infoHTML;
    }

    renderRouteView() {
        if (this.routeRenderer && this.routeScene && this.routeCamera) {
            this.routeRenderer.render(this.routeScene, this.routeCamera);
        }
    }

    planRouteTo(poiId) {
        // 规划到指定POI的路线
        const poi = this.dataManager.getPOI(poiId);
        if (!poi) return;
        
        // 创建简单的点对点路线
        const quickRoute = {
            id: 'route_to_poi',
            name: `前往${poi.name}`,
            description: `直接前往${poi.name}的最短路线`,
            duration: poi.visitDuration + 10,
            difficulty: 'easy',
            distance: 0.5,
            pois: [poiId],
            path: [
                { x: 0, y: 0, z: 0 }, // 当前位置（假设）
                poi.position
            ],
            highlights: [poi.name],
            tips: '请注意安全，按照指示路线前往'
        };
        
        this.selectRoute(quickRoute);
        this.showRouteNotification(`已规划前往${poi.name}的路线`, 'success');
    }

    highlightPOIInRoute(poiId) {
        // 在路线中高亮显示指定POI
        this.routeMarkers.forEach(marker => {
            if (marker.userData && marker.userData.poiId === poiId) {
                // 添加高亮效果
                if (marker.material) {
                    marker.material.emissive = new THREE.Color(0x444444);
                }
            }
        });
        this.renderRouteView();
    }

    showRouteNotification(message, type = 'info') {
        // 显示路线相关通知
        window.dispatchEvent(new CustomEvent('showNotification', {
            detail: { message, type }
        }));
    }

    handleRouteViewResize() {
        if (this.routeRenderer && this.route3DView) {
            const width = this.route3DView.clientWidth;
            const height = this.route3DView.clientHeight;
            
            this.routeRenderer.setSize(width, height);
            this.routeCamera.aspect = width / height;
            this.routeCamera.updateProjectionMatrix();
            this.renderRouteView();
        }
    }

    pause() {
        // 暂停路线管理器
        this.isNavigating = false;
    }

    resume() {
        // 恢复路线管理器
        if (this.currentRoute) {
            this.renderRouteView();
        }
    }

    dispose() {
        this.clearRouteVisualization();
        
        if (this.routeRenderer) {
            this.routeRenderer.dispose();
        }
    }
}
