// 探宝游戏管理器 - 处理AR解密探宝游戏功能
export class TreasureManager {
    constructor(arManager, dataManager) {
        this.arManager = arManager;
        this.dataManager = dataManager;
        
        this.gameState = {
            isActive: false,
            currentTreasure: null,
            foundTreasures: [],
            totalScore: 0,
            hints: [],
            timeStarted: null
        };
        
        this.treasureMarkers = new Map();
        this.riddleDialog = null;
        this.proximityThreshold = 10; // 米
        
        // UI元素
        this.treasureContainer = document.getElementById('treasure-container');
        this.treasureHUD = document.querySelector('.treasure-hud');
        this.treasureARView = document.getElementById('treasure-ar-view');
        this.scoreDisplay = document.getElementById('treasure-points');
        this.treasuresFoundDisplay = document.getElementById('treasures-found');
        this.hintDisplay = document.getElementById('treasure-hint-text');
        
        this.isInitialized = false;
    }

    async init() {
        try {
            this.setupTreasureGame();
            this.createTreasureMarkers();
            this.bindEvents();
            this.updateUI();
            
            this.isInitialized = true;
            console.log('探宝游戏管理器初始化完成');
        } catch (error) {
            console.error('探宝游戏管理器初始化失败:', error);
            throw error;
        }
    }

    setupTreasureGame() {
        // 初始化游戏状态
        const userProgress = this.dataManager.getUserProgress();
        this.gameState.foundTreasures = userProgress.foundTreasures || [];
        this.gameState.totalScore = userProgress.totalScore || 0;
        
        // 设置AR视图
        this.setupTreasureARView();
    }

    setupTreasureARView() {
        // 创建探宝专用的AR场景
        const arScene = document.createElement('a-scene');
        arScene.setAttribute('embedded', true);
        arScene.setAttribute('arjs', 'sourceType: webcam; debugUIEnabled: false;');
        arScene.setAttribute('vr-mode-ui', 'enabled: false');
        
        // 添加AR相机
        const arCamera = document.createElement('a-camera');
        arCamera.setAttribute('gps-camera', 'gpsMinDistance: 5');
        arCamera.setAttribute('rotation-reader', '');
        arScene.appendChild(arCamera);
        
        this.treasureARView.appendChild(arScene);
        this.treasureARScene = arScene;
    }

    createTreasureMarkers() {
        const treasures = this.dataManager.getAllTreasures();
        
        treasures.forEach(treasure => {
            if (!this.gameState.foundTreasures.includes(treasure.id)) {
                this.createTreasureMarker(treasure);
            }
        });
    }

    createTreasureMarker(treasure) {
        // 创建宝藏AR标记
        const marker = document.createElement('a-entity');
        marker.setAttribute('gps-entity-place', 
            `latitude: ${treasure.position.x}; longitude: ${treasure.position.z}`);
        marker.setAttribute('look-at', '[gps-camera]');
        
        // 创建宝藏3D模型
        const treasureModel = this.createTreasureModel(treasure);
        marker.appendChild(treasureModel);
        
        // 添加到AR场景
        this.treasureARScene.appendChild(marker);
        this.treasureMarkers.set(treasure.id, marker);
        
        // 添加点击事件
        marker.addEventListener('click', () => {
            this.onTreasureClicked(treasure);
        });
    }

    createTreasureModel(treasure) {
        const group = document.createElement('a-entity');
        
        // 宝藏容器
        const container = document.createElement('a-box');
        container.setAttribute('position', '0 0 0');
        container.setAttribute('width', '1');
        container.setAttribute('height', '1');
        container.setAttribute('depth', '1');
        container.setAttribute('color', this.getTreasureColor(treasure.type));
        container.setAttribute('metalness', '0.8');
        container.setAttribute('roughness', '0.2');
        
        // 发光效果
        container.setAttribute('animation', 
            'property: rotation; to: 0 360 0; loop: true; dur: 4000');
        
        // 粒子效果
        const particles = document.createElement('a-entity');
        particles.setAttribute('particle-system', 
            'preset: dust; particleCount: 100; color: #FFD700');
        particles.setAttribute('position', '0 0.5 0');
        
        // 信息标签
        const label = document.createElement('a-text');
        label.setAttribute('value', '?');
        label.setAttribute('position', '0 1.5 0');
        label.setAttribute('align', 'center');
        label.setAttribute('color', '#FFD700');
        label.setAttribute('width', '8');
        
        group.appendChild(container);
        group.appendChild(particles);
        group.appendChild(label);
        
        return group;
    }

    getTreasureColor(type) {
        const colors = {
            historical: '#8B4513',
            geological: '#4169E1',
            fossil: '#228B22',
            artifact: '#FFD700'
        };
        return colors[type] || '#666666';
    }

    bindEvents() {
        // 设备位置变化监听
        if (navigator.geolocation) {
            navigator.geolocation.watchPosition(
                (position) => this.onLocationUpdate(position),
                (error) => console.warn('位置获取失败:', error),
                { enableHighAccuracy: true, maximumAge: 30000, timeout: 27000 }
            );
        }

        // 设备方向变化
        window.addEventListener('deviceorientationabsolute', (event) => {
            this.updateTreasureHints(event);
        });

        // 游戏控制事件
        window.addEventListener('startTreasureHunt', () => {
            this.startGame();
        });

        window.addEventListener('pauseTreasureHunt', () => {
            this.pauseGame();
        });
    }

    onLocationUpdate(position) {
        if (!this.gameState.isActive) return;
        
        const userLat = position.coords.latitude;
        const userLng = position.coords.longitude;
        
        // 检查是否接近宝藏
        this.checkTreasureProximity(userLat, userLng);
        
        // 更新提示
        this.updateProximityHints(userLat, userLng);
    }

    checkTreasureProximity(userLat, userLng) {
        const treasures = this.dataManager.getAllTreasures();
        
        treasures.forEach(treasure => {
            if (this.gameState.foundTreasures.includes(treasure.id)) return;
            
            const distance = this.calculateDistance(
                userLat, userLng,
                treasure.position.x, treasure.position.z
            );
            
            if (distance <= this.proximityThreshold) {
                this.triggerTreasureDiscovery(treasure);
            }
        });
    }

    calculateDistance(lat1, lng1, lat2, lng2) {
        // 计算两点间距离（简化版本）
        const R = 6371000; // 地球半径（米）
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    updateProximityHints(userLat, userLng) {
        const treasures = this.dataManager.getAllTreasures();
        let nearestTreasure = null;
        let minDistance = Infinity;
        
        treasures.forEach(treasure => {
            if (this.gameState.foundTreasures.includes(treasure.id)) return;
            
            const distance = this.calculateDistance(
                userLat, userLng,
                treasure.position.x, treasure.position.z
            );
            
            if (distance < minDistance) {
                minDistance = distance;
                nearestTreasure = treasure;
            }
        });
        
        if (nearestTreasure) {
            this.updateHintDisplay(nearestTreasure, minDistance);
        }
    }

    updateHintDisplay(treasure, distance) {
        let hintText = '';
        
        if (distance > 100) {
            hintText = `🧭 ${treasure.hint} (距离: ${Math.round(distance)}米)`;
        } else if (distance > 50) {
            hintText = `🔍 你正在接近宝藏！继续寻找... (${Math.round(distance)}米)`;
        } else if (distance > 20) {
            hintText = `🎯 很近了！${treasure.riddle}`;
        } else {
            hintText = `⭐ 就在附近！仔细观察周围环境`;
        }
        
        this.hintDisplay.textContent = hintText;
    }

    triggerTreasureDiscovery(treasure) {
        if (this.gameState.foundTreasures.includes(treasure.id)) return;
        
        // 显示解密挑战
        this.showRiddleChallenge(treasure);
    }

    showRiddleChallenge(treasure) {
        // 创建解密对话框
        this.riddleDialog = document.createElement('div');
        this.riddleDialog.className = 'riddle-dialog';
        this.riddleDialog.innerHTML = `
            <div class="riddle-content">
                <h3>🏺 发现宝藏！</h3>
                <div class="treasure-image">
                    <div class="treasure-icon" style="background-color: ${this.getTreasureColor(treasure.type)}">
                        ${this.getTreasureIcon(treasure.type)}
                    </div>
                </div>
                <h4>${treasure.name}</h4>
                <p class="riddle-text">${treasure.riddle}</p>
                <div class="riddle-input">
                    <input type="text" id="riddle-answer" placeholder="输入答案...">
                    <button id="submit-answer" class="primary-btn">提交</button>
                </div>
                <div class="riddle-actions">
                    <button id="riddle-hint" class="hint-btn">💡 提示</button>
                    <button id="riddle-skip" class="skip-btn">跳过</button>
                </div>
            </div>
        `;
        
        // 添加样式
        this.riddleDialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        document.body.appendChild(this.riddleDialog);
        
        // 绑定事件
        this.bindRiddleEvents(treasure);
    }

    bindRiddleEvents(treasure) {
        const answerInput = document.getElementById('riddle-answer');
        const submitBtn = document.getElementById('submit-answer');
        const hintBtn = document.getElementById('riddle-hint');
        const skipBtn = document.getElementById('riddle-skip');
        
        submitBtn.addEventListener('click', () => {
            this.checkRiddleAnswer(treasure, answerInput.value);
        });
        
        answerInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.checkRiddleAnswer(treasure, answerInput.value);
            }
        });
        
        hintBtn.addEventListener('click', () => {
            this.showRiddleHint(treasure);
        });
        
        skipBtn.addEventListener('click', () => {
            this.skipRiddle(treasure);
        });
    }

    checkRiddleAnswer(treasure, answer) {
        // 简化的答案检查（实际应用中可以更复杂）
        const correctAnswers = this.getTreasureAnswers(treasure);
        const userAnswer = answer.toLowerCase().trim();
        
        const isCorrect = correctAnswers.some(correct => 
            userAnswer.includes(correct.toLowerCase())
        );
        
        if (isCorrect) {
            this.onTreasureFound(treasure, true);
        } else {
            this.showWrongAnswer();
        }
    }

    getTreasureAnswers(treasure) {
        // 根据宝藏类型返回可能的正确答案
        const answers = {
            'treasure_001': ['镐', '工具', '矿工镐'],
            'treasure_002': ['石英', '水晶', '矿物'],
            'treasure_003': ['化石', '植物', '古代']
        };
        return answers[treasure.id] || ['宝藏'];
    }

    showWrongAnswer() {
        const answerInput = document.getElementById('riddle-answer');
        answerInput.style.borderColor = '#e74c3c';
        answerInput.placeholder = '答案不正确，请重试...';
        answerInput.value = '';
        
        setTimeout(() => {
            answerInput.style.borderColor = '';
            answerInput.placeholder = '输入答案...';
        }, 2000);
    }

    showRiddleHint(treasure) {
        const hintText = `💡 提示: ${treasure.hint}`;
        this.showTreasureNotification(hintText, 'info', 5000);
    }

    skipRiddle(treasure) {
        this.onTreasureFound(treasure, false);
    }

    onTreasureFound(treasure, solvedRiddle) {
        // 关闭解密对话框
        if (this.riddleDialog) {
            document.body.removeChild(this.riddleDialog);
            this.riddleDialog = null;
        }
        
        // 计算得分
        let points = treasure.points;
        if (solvedRiddle) {
            points *= 1.5; // 解密成功额外奖励
        }
        
        // 更新游戏状态
        this.gameState.foundTreasures.push(treasure.id);
        this.gameState.totalScore += points;
        
        // 更新数据管理器
        this.dataManager.markTreasureFound(treasure.id);
        
        // 移除AR标记
        const marker = this.treasureMarkers.get(treasure.id);
        if (marker) {
            this.treasureARScene.removeChild(marker);
            this.treasureMarkers.delete(treasure.id);
        }
        
        // 显示成功动画
        this.showTreasureFoundAnimation(treasure, points, solvedRiddle);
        
        // 更新UI
        this.updateUI();
        
        // 检查是否完成所有宝藏
        this.checkGameCompletion();
    }

    showTreasureFoundAnimation(treasure, points, solvedRiddle) {
        const message = solvedRiddle ? 
            `🎉 解密成功！发现 ${treasure.name} (+${points}分)` :
            `📦 发现 ${treasure.name} (+${points}分)`;
        
        this.showTreasureNotification(message, 'success', 4000);
        
        // 添加庆祝效果
        this.createCelebrationEffect();
    }

    createCelebrationEffect() {
        // 创建庆祝粒子效果
        const celebration = document.createElement('div');
        celebration.className = 'celebration-effect';
        celebration.innerHTML = '🎉✨🏆✨🎉';
        celebration.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 3rem;
            z-index: 9999;
            animation: celebration 2s ease-out forwards;
        `;
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes celebration {
                0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
                50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
                100% { transform: translate(-50%, -50%) scale(1) translateY(-100px); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(celebration);
        
        setTimeout(() => {
            if (celebration.parentNode) {
                celebration.parentNode.removeChild(celebration);
            }
            if (style.parentNode) {
                style.parentNode.removeChild(style);
            }
        }, 2000);
    }

    checkGameCompletion() {
        const totalTreasures = this.dataManager.getAllTreasures().length;
        const foundCount = this.gameState.foundTreasures.length;
        
        if (foundCount >= totalTreasures) {
            this.onGameCompleted();
        }
    }

    onGameCompleted() {
        const completionMessage = `
            🏆 恭喜完成探宝游戏！
            
            📊 游戏统计：
            • 发现宝藏：${this.gameState.foundTreasures.length}个
            • 总得分：${this.gameState.totalScore}分
            • 游戏时长：${this.getGameDuration()}
            
            🎁 你已解锁特殊成就徽章！
        `;
        
        this.showTreasureNotification(completionMessage, 'success', 10000);
        
        // 保存完成记录
        this.dataManager.markRouteCompleted('treasure_hunt');
    }

    getGameDuration() {
        if (!this.gameState.timeStarted) return '未知';
        
        const duration = Date.now() - this.gameState.timeStarted;
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        
        return `${minutes}分${seconds}秒`;
    }

    getTreasureIcon(type) {
        const icons = {
            historical: '⚒️',
            geological: '💎',
            fossil: '🦕',
            artifact: '🏺'
        };
        return icons[type] || '💰';
    }

    updateUI() {
        // 更新得分显示
        this.scoreDisplay.textContent = this.gameState.totalScore;
        
        // 更新宝藏计数
        const totalTreasures = this.dataManager.getAllTreasures().length;
        this.treasuresFoundDisplay.textContent = this.gameState.foundTreasures.length;
        
        // 更新进度条（如果有）
        const progressBar = document.querySelector('.treasure-progress');
        if (progressBar) {
            const progress = (this.gameState.foundTreasures.length / totalTreasures) * 100;
            progressBar.style.width = `${progress}%`;
        }
    }

    showTreasureNotification(message, type = 'info', duration = 3000) {
        // 显示探宝相关通知
        window.dispatchEvent(new CustomEvent('showNotification', {
            detail: { message, type, duration }
        }));
    }

    startGame() {
        this.gameState.isActive = true;
        this.gameState.timeStarted = Date.now();
        this.hintDisplay.textContent = '🗺️ 探宝游戏开始！使用AR扫描周围环境寻找宝藏';
        this.showTreasureNotification('探宝游戏开始！', 'success');
    }

    pauseGame() {
        this.gameState.isActive = false;
        this.hintDisplay.textContent = '游戏已暂停';
    }

    pause() {
        this.pauseGame();
    }

    resume() {
        if (this.gameState.foundTreasures.length < this.dataManager.getAllTreasures().length) {
            this.startGame();
        }
    }

    dispose() {
        this.pauseGame();
        
        // 清理AR标记
        this.treasureMarkers.clear();
        
        // 移除对话框
        if (this.riddleDialog && this.riddleDialog.parentNode) {
            this.riddleDialog.parentNode.removeChild(this.riddleDialog);
        }
    }
}
