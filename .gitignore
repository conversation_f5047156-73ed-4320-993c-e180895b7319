# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/

# 缓存文件
.cache/
.parcel-cache/

# 测试覆盖率
coverage/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 本地配置文件
config.local.js
settings.local.json
