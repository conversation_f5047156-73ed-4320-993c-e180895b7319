# 资源文件说明

本目录包含汤山矿坑公园三维导览系统的所有静态资源文件。

## 目录结构

```
assets/
├── images/          # 图片资源
│   ├── poi/        # 景点图片
│   ├── ui/         # 界面图标
│   └── backgrounds/ # 背景图片
├── models/          # 3D模型文件
│   ├── terrain/    # 地形模型
│   ├── buildings/  # 建筑模型
│   └── vegetation/ # 植被模型
├── textures/        # 纹理贴图
│   ├── terrain/    # 地形纹理
│   ├── water/      # 水面纹理
│   └── sky/        # 天空纹理
├── audio/           # 音频文件
│   ├── guides/     # 语音导览
│   ├── ambient/    # 环境音效
│   └── ui/         # 界面音效
└── markers/         # AR标记文件
    ├── poi/        # 景点标记
    └── treasure/   # 宝藏标记
```

## 文件格式说明

### 图片文件
- **格式**: JPG, PNG, WebP
- **分辨率**: 建议1920x1080或更高
- **压缩**: 优化文件大小，保持质量

### 3D模型文件
- **格式**: GLTF, GLB (推荐), FBX, OBJ
- **优化**: 减少面数，合并材质
- **纹理**: 使用压缩纹理格式

### 音频文件
- **格式**: MP3, OGG, WAV
- **质量**: 128kbps - 320kbps
- **时长**: 导览音频建议1-3分钟

### AR标记文件
- **格式**: .patt (AR.js标记文件)
- **生成**: 使用AR.js Marker Training工具
- **尺寸**: 建议16x16或32x32

## 资源优化建议

1. **图片优化**
   - 使用WebP格式减少文件大小
   - 提供多种分辨率版本
   - 使用懒加载技术

2. **模型优化**
   - 使用LOD (Level of Detail) 技术
   - 合并相似材质
   - 压缩纹理贴图

3. **音频优化**
   - 使用适当的比特率
   - 考虑使用流式播放
   - 提供多语言版本

4. **缓存策略**
   - 设置合适的缓存头
   - 使用CDN加速
   - 实现渐进式加载

## 版权说明

所有资源文件应确保拥有合法使用权限，包括：
- 自主创作的原创内容
- 购买的商业授权素材
- 符合开源协议的免费素材

请在使用第三方资源时注明来源和版权信息。
