// UI管理器 - 处理用户界面交互和显示
export class UIManager {
    constructor() {
        this.panels = {
            info: document.getElementById('info-panel'),
            settings: document.getElementById('settings-panel')
        };
        this.isInitialized = false;
    }

    init() {
        this.bindEvents();
        this.setupSettings();
        this.isInitialized = true;
        console.log('UI管理器初始化完成');
    }

    bindEvents() {
        // 信息面板关闭按钮
        document.getElementById('close-info').addEventListener('click', () => {
            this.hideInfo();
        });

        // 设置面板关闭按钮
        document.getElementById('close-settings').addEventListener('click', () => {
            this.hideSettings();
        });

        // 点击面板外部关闭
        this.panels.info.addEventListener('click', (e) => {
            if (e.target === this.panels.info) {
                this.hideInfo();
            }
        });

        this.panels.settings.addEventListener('click', (e) => {
            if (e.target === this.panels.settings) {
                this.hideSettings();
            }
        });

        // ESC键关闭面板
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllPanels();
            }
        });
    }

    setupSettings() {
        // 图形质量设置
        const qualitySelect = document.getElementById('quality-select');
        qualitySelect.addEventListener('change', (e) => {
            this.updateGraphicsQuality(e.target.value);
        });

        // 音量设置
        const volumeSlider = document.getElementById('volume-slider');
        volumeSlider.addEventListener('input', (e) => {
            this.updateVolume(e.target.value);
        });

        // 自动旋转设置
        const autoRotateCheckbox = document.getElementById('auto-rotate');
        autoRotateCheckbox.addEventListener('change', (e) => {
            this.updateAutoRotate(e.target.checked);
        });

        // 加载保存的设置
        this.loadSettings();
    }

    // 显示信息面板
    showInfo(title, content) {
        const infoBody = document.getElementById('info-body');
        infoBody.innerHTML = `
            <h2>${title}</h2>
            <div class="info-content-body">
                ${content}
            </div>
        `;
        this.panels.info.classList.remove('hidden');
        
        // 添加动画效果
        this.panels.info.style.opacity = '0';
        this.panels.info.style.transform = 'scale(0.9)';
        
        requestAnimationFrame(() => {
            this.panels.info.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            this.panels.info.style.opacity = '1';
            this.panels.info.style.transform = 'scale(1)';
        });
    }

    // 隐藏信息面板
    hideInfo() {
        this.panels.info.style.opacity = '0';
        this.panels.info.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            this.panels.info.classList.add('hidden');
            this.panels.info.style.transition = '';
        }, 300);
    }

    // 显示设置面板
    showSettings() {
        this.panels.settings.classList.remove('hidden');
        
        // 添加动画效果
        this.panels.settings.style.opacity = '0';
        this.panels.settings.style.transform = 'scale(0.9)';
        
        requestAnimationFrame(() => {
            this.panels.settings.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            this.panels.settings.style.opacity = '1';
            this.panels.settings.style.transform = 'scale(1)';
        });
    }

    // 隐藏设置面板
    hideSettings() {
        this.panels.settings.style.opacity = '0';
        this.panels.settings.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            this.panels.settings.classList.add('hidden');
            this.panels.settings.style.transition = '';
        }, 300);
    }

    // 显示帮助信息
    showHelp() {
        const helpContent = `
            <h3>操作指南</h3>
            <div class="help-section">
                <h4>🖱️ 鼠标操作</h4>
                <ul>
                    <li>左键拖拽：旋转视角</li>
                    <li>右键拖拽：平移视角</li>
                    <li>滚轮：缩放视角</li>
                    <li>双击：聚焦到景点</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h4>⌨️ 键盘快捷键</h4>
                <ul>
                    <li>1-4：切换功能模式</li>
                    <li>R：重置相机视角</li>
                    <li>Ctrl+H：显示帮助</li>
                    <li>ESC：关闭面板</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h4>📱 移动设备</h4>
                <ul>
                    <li>单指拖拽：旋转视角</li>
                    <li>双指缩放：调整视距</li>
                    <li>双指拖拽：平移视角</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h4>🎯 AR功能</h4>
                <ul>
                    <li>允许相机权限</li>
                    <li>将设备对准景点</li>
                    <li>点击AR标记获取信息</li>
                    <li>移动设备寻找宝藏</li>
                </ul>
            </div>
        `;
        
        this.showInfo('使用帮助', helpContent);
    }

    // 关闭所有面板
    closeAllPanels() {
        this.hideInfo();
        this.hideSettings();
    }

    // 显示通知消息
    showNotification(message, type = 'info', duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${this.getNotificationIcon(type)}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">×</button>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 90px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        // 关闭按钮事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.hideNotification(notification);
        });

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification(notification);
            }, duration);
        }

        return notification;
    }

    // 隐藏通知
    hideNotification(notification) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌'
        };
        return icons[type] || icons.info;
    }

    // 获取通知颜色
    getNotificationColor(type) {
        const colors = {
            info: 'linear-gradient(45deg, #3498db, #2980b9)',
            success: 'linear-gradient(45deg, #2ecc71, #27ae60)',
            warning: 'linear-gradient(45deg, #f39c12, #e67e22)',
            error: 'linear-gradient(45deg, #e74c3c, #c0392b)'
        };
        return colors[type] || colors.info;
    }

    // 更新图形质量
    updateGraphicsQuality(quality) {
        const settings = this.getSettings();
        settings.graphicsQuality = quality;
        this.saveSettings(settings);
        
        // 触发图形质量更新事件
        window.dispatchEvent(new CustomEvent('graphicsQualityChanged', { 
            detail: { quality } 
        }));
        
        this.showNotification(`图形质量已设置为：${quality}`, 'success');
    }

    // 更新音量
    updateVolume(volume) {
        const settings = this.getSettings();
        settings.volume = parseInt(volume);
        this.saveSettings(settings);
        
        // 触发音量更新事件
        window.dispatchEvent(new CustomEvent('volumeChanged', { 
            detail: { volume: settings.volume } 
        }));
    }

    // 更新自动旋转
    updateAutoRotate(enabled) {
        const settings = this.getSettings();
        settings.autoRotate = enabled;
        this.saveSettings(settings);
        
        // 触发自动旋转更新事件
        window.dispatchEvent(new CustomEvent('autoRotateChanged', { 
            detail: { enabled } 
        }));
    }

    // 获取设置
    getSettings() {
        const defaultSettings = {
            graphicsQuality: 'medium',
            volume: 50,
            autoRotate: true
        };
        
        const saved = localStorage.getItem('tangshan_park_settings');
        return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    }

    // 保存设置
    saveSettings(settings) {
        localStorage.setItem('tangshan_park_settings', JSON.stringify(settings));
    }

    // 加载设置
    loadSettings() {
        const settings = this.getSettings();

        document.getElementById('quality-select').value = settings.graphicsQuality;
        document.getElementById('volume-slider').value = settings.volume;
        document.getElementById('auto-rotate').checked = settings.autoRotate;
    }

    // 显示景点信息
    showPOIInfo(poi) {
        const content = `
            <div class="poi-info">
                <div class="poi-images">
                    ${poi.images.map(img => `<img src="${img}" alt="${poi.name}" style="width: 100%; margin-bottom: 10px; border-radius: 8px;">`).join('')}
                </div>
                <div class="poi-details">
                    <p><strong>类型：</strong>${this.getPOITypeText(poi.type)}</p>
                    <p><strong>建议游览时间：</strong>${poi.visitDuration}分钟</p>
                    <p><strong>开放时间：</strong>${poi.openTime}</p>
                    <p><strong>设施：</strong>${poi.facilities.join('、')}</p>
                    <p><strong>游览提示：</strong>${poi.tips}</p>
                </div>
                <div class="poi-description">
                    <p>${poi.description}</p>
                </div>
                <div class="poi-actions">
                    <button class="primary-btn" onclick="window.TangshanParkApp.getManager('route').planRouteTo('${poi.id}')">
                        规划路线到此
                    </button>
                    <button class="primary-btn" onclick="window.TangshanParkApp.getManager('ar').focusOnPOI('${poi.id}')">
                        AR查看
                    </button>
                </div>
            </div>
        `;
        this.showInfo(poi.name, content);
    }

    // 获取景点类型文本
    getPOITypeText(type) {
        const types = {
            lake: '湖泊景观',
            viewpoint: '观景点',
            trail: '步道',
            museum: '博物馆',
            playground: '游乐区'
        };
        return types[type] || type;
    }
}
