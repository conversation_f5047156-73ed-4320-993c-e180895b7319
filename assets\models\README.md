# 汤山矿坑公园3D模型资源

本目录包含汤山矿坑公园三维导览系统所需的所有3D模型文件。

## 📁 目录结构

```
models/
├── terrain/              # 地形模型
│   ├── tangshan_mine_terrain.glb    # 主要矿坑地形
│   ├── surrounding_hills.glb        # 周边山体
│   └── lake_bottom.glb              # 湖底地形
├── buildings/            # 建筑模型
│   ├── viewing_platform.glb         # 观景平台
│   ├── geological_museum.glb        # 地质博物馆
│   ├── visitor_center.glb           # 游客中心
│   ├── playground_equipment.glb     # 游乐设施
│   └── rest_pavilion.glb           # 休息亭
├── vegetation/           # 植被模型
│   ├── large_trees_pack.glb        # 大型树木包
│   ├── bushes_pack.glb             # 灌木丛包
│   └── aquatic_plants.glb          # 水生植物
├── details/              # 细节装饰
│   ├── street_light.glb            # 路灯
│   ├── sign_boards.glb             # 指示牌
│   ├── park_bench.glb              # 座椅
│   └── trash_bin.glb               # 垃圾桶
├── effects/              # 特效模型
│   ├── water_splash.glb            # 水花效果
│   └── flying_birds.glb            # 飞鸟动画
└── materials/            # 材质文件
    ├── terrain_materials.mtl
    ├── building_materials.mtl
    └── vegetation_materials.mtl
```

## 🎯 模型规范

### 文件格式要求
- **推荐格式**: GLTF 2.0 (.glb)
- **备选格式**: FBX (.fbx), OBJ+MTL (.obj/.mtl)
- **压缩**: 支持DRACO几何压缩和KTX2纹理压缩

### 性能要求
| 模型类型 | 最大三角形数 | 纹理分辨率 | 文件大小 |
|---------|-------------|-----------|----------|
| 地形模型 | 100,000 | 2048x2048 | < 50MB |
| 建筑模型 | 50,000 | 1024x1024 | < 20MB |
| 植被模型 | 10,000 | 512x512 | < 5MB |
| 细节模型 | 5,000 | 256x256 | < 2MB |

### 坐标系统
- **单位**: 米 (meter)
- **坐标系**: 右手坐标系，Y轴向上
- **原点**: 矿坑湖中心为世界坐标原点 (0, 0, 0)
- **方向**: X轴指向东，Z轴指向北

### 材质规范
- **PBR材质**: 使用基于物理的渲染材质
- **纹理格式**: PNG (透明) / JPG (不透明)
- **法线贴图**: 使用OpenGL格式 (Y+)
- **金属度/粗糙度**: 分离贴图或打包贴图

## 🏗️ 模型制作指南

### 建模软件推荐
- **Blender** (免费，推荐)
- **3ds Max** (商业软件)
- **Maya** (商业软件)
- **SketchUp** (建筑建模)

### 导出设置

#### Blender导出GLTF设置
```
格式: glTF Binary (.glb)
包含: 
  ✓ 网格
  ✓ 材质
  ✓ 纹理
  ✓ 动画 (如有)
几何体:
  ✓ 应用修改器
  ✓ UVs
  ✓ 法线
  ✓ 顶点颜色
材质:
  ✓ 导出材质
  ✓ 导出图像
压缩:
  ✓ DRACO网格压缩
```

#### 3ds Max导出FBX设置
```
版本: FBX 2020
几何体:
  ✓ 平滑组
  ✓ 切线和副法线
  ✓ 保留边界
材质:
  ✓ 嵌入媒体
  ✓ 纹理
动画:
  ✓ 动画 (如需要)
单位: 米
```

### 优化建议

#### 几何体优化
- 合理控制面数，避免过度细分
- 移除不可见的面和顶点
- 使用LOD (Level of Detail) 技术
- 合并相似的网格对象

#### 纹理优化
- 使用适当的纹理分辨率
- 合并小纹理到纹理图集
- 使用压缩格式 (DXT/BC7)
- 避免过大的纹理文件

#### 材质优化
- 减少材质数量
- 使用实例化材质
- 合理设置材质属性
- 避免透明材质的过度使用

## 📦 模型打包

### 文件命名规范
```
[类别]_[名称]_[版本].[扩展名]

示例:
- building_museum_v1.glb
- terrain_main_v2.glb
- vegetation_trees_pack_v1.glb
```

### 版本控制
- v1: 初始版本
- v2: 重大更新
- v2.1: 小幅修改

### 文件组织
```
每个模型包含:
├── model.glb              # 主模型文件
├── preview.jpg            # 预览图片
├── info.json             # 模型信息
└── textures/             # 纹理文件夹 (如需要)
    ├── diffuse.jpg
    ├── normal.jpg
    └── roughness.jpg
```

## 🔧 技术集成

### 加载配置
模型通过 `src/config/models.js` 文件进行配置：

```javascript
export const MODEL_CONFIGS = {
    buildings: {
        museum: {
            type: 'gltf',
            url: '/assets/models/buildings/geological_museum.glb',
            options: {
                scale: 1.0,
                position: { x: 80, y: 10, z: -20 },
                castShadow: true,
                receiveShadow: true
            },
            priority: 'high'
        }
    }
};
```

### 性能监控
系统会自动监控模型性能：
- 三角形数量统计
- 内存使用监控
- 渲染性能分析
- 自动LOD切换

## 📋 模型清单

### 已完成模型
- [ ] 主要矿坑地形
- [ ] 观景平台建筑
- [ ] 地质博物馆
- [ ] 游客服务中心
- [ ] 儿童游乐设施

### 待制作模型
- [ ] 周边山体地形
- [ ] 湖底细节地形
- [ ] 大型景观树木
- [ ] 灌木丛植被
- [ ] 水生植物
- [ ] 园区路灯
- [ ] 导览指示牌
- [ ] 休息座椅
- [ ] 垃圾桶等设施

## 🎨 美术风格指南

### 整体风格
- **写实风格**: 基于真实汤山矿坑公园
- **色彩搭配**: 自然色调，绿色植被，蓝色水体
- **光照**: 自然光照，暖色调

### 材质风格
- **地形**: 自然岩石纹理，风化效果
- **建筑**: 现代简约风格，混凝土和玻璃材质
- **植被**: 真实植物纹理，季节变化
- **水体**: 清澈透明，反射效果

## 📞 技术支持

如有模型制作或集成问题，请参考：
- 项目文档: README.md
- 技术规范: TECHNICAL_SPECS.md
- 问题反馈: GitHub Issues

---

**注意**: 所有模型文件应确保拥有合法使用权限，遵守相关版权法规。
