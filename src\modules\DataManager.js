// 数据管理器 - 管理景点数据、路线数据、用户数据等
export class DataManager {
    constructor() {
        this.pointsOfInterest = [];
        this.routes = [];
        this.treasures = [];
        this.userProgress = {
            visitedPOIs: [],
            foundTreasures: [],
            completedRoutes: [],
            totalScore: 0
        };
        this.isInitialized = false;
    }

    async init() {
        try {
            // 加载本地数据或从服务器获取数据
            await this.loadPOIData();
            await this.loadRouteData();
            await this.loadTreasureData();
            await this.loadUserProgress();
            
            this.isInitialized = true;
            console.log('数据管理器初始化完成');
        } catch (error) {
            console.error('数据管理器初始化失败:', error);
            throw error;
        }
    }

    // 加载景点数据
    async loadPOIData() {
        // 汤山矿坑公园的主要景点数据
        this.pointsOfInterest = [
            {
                id: 'poi_001',
                name: '矿坑湖',
                description: '由废弃矿坑形成的人工湖泊，湖水清澈，周围植被茂盛，是公园的核心景观。',
                position: { x: 0, y: 0, z: 0 },
                type: 'lake',
                importance: 5,
                visitDuration: 30, // 分钟
                images: ['/assets/images/poi/mine_lake_1.jpg', '/assets/images/poi/mine_lake_2.jpg'],
                audioGuide: '/assets/audio/mine_lake_guide.mp3',
                arMarkers: ['marker_lake_1', 'marker_lake_2'],
                facilities: ['观景台', '休息亭', '拍照点'],
                openTime: '全天开放',
                tips: '最佳拍照时间为日出和日落时分'
            },
            {
                id: 'poi_002',
                name: '观景平台',
                description: '位于矿坑边缘的观景平台，可俯瞰整个矿坑湖和周围景色。',
                position: { x: 50, y: 20, z: 30 },
                type: 'viewpoint',
                importance: 4,
                visitDuration: 20,
                images: ['/assets/images/poi/viewpoint_1.jpg'],
                audioGuide: '/assets/audio/viewpoint_guide.mp3',
                arMarkers: ['marker_viewpoint'],
                facilities: ['望远镜', '座椅', '安全护栏'],
                openTime: '6:00-22:00',
                tips: '建议携带相机，这里是最佳摄影点'
            },
            {
                id: 'poi_003',
                name: '生态步道',
                description: '环绕矿坑湖的生态步道，全长约2公里，沿途可观赏各种植物和鸟类。',
                position: { x: -30, y: 5, z: 20 },
                type: 'trail',
                importance: 3,
                visitDuration: 45,
                images: ['/assets/images/poi/trail_1.jpg', '/assets/images/poi/trail_2.jpg'],
                audioGuide: '/assets/audio/trail_guide.mp3',
                arMarkers: ['marker_trail_start', 'marker_trail_mid', 'marker_trail_end'],
                facilities: ['指示牌', '休息点', '垃圾桶'],
                openTime: '全天开放',
                tips: '建议穿着舒适的运动鞋'
            },
            {
                id: 'poi_004',
                name: '地质博物馆',
                description: '展示汤山地区地质历史和矿物标本的小型博物馆。',
                position: { x: 80, y: 10, z: -20 },
                type: 'museum',
                importance: 4,
                visitDuration: 40,
                images: ['/assets/images/poi/museum_1.jpg'],
                audioGuide: '/assets/audio/museum_guide.mp3',
                arMarkers: ['marker_museum'],
                facilities: ['展览厅', '多媒体设备', '纪念品店'],
                openTime: '9:00-17:00',
                tips: '周一闭馆，建议提前了解开放时间'
            },
            {
                id: 'poi_005',
                name: '儿童游乐区',
                description: '专为儿童设计的游乐区域，包含多种安全的游乐设施。',
                position: { x: -60, y: 0, z: -40 },
                type: 'playground',
                importance: 2,
                visitDuration: 60,
                images: ['/assets/images/poi/playground_1.jpg'],
                audioGuide: '/assets/audio/playground_guide.mp3',
                arMarkers: ['marker_playground'],
                facilities: ['滑梯', '秋千', '沙坑', '安全围栏'],
                openTime: '8:00-18:00',
                tips: '建议家长陪同，注意儿童安全'
            }
        ];
    }

    // 加载路线数据
    async loadRouteData() {
        this.routes = [
            {
                id: 'route_recommended',
                name: '推荐路线',
                description: '涵盖主要景点的经典游览路线，适合首次游客',
                duration: 120, // 分钟
                difficulty: 'easy',
                distance: 2.5, // 公里
                pois: ['poi_001', 'poi_002', 'poi_003', 'poi_004'],
                path: [
                    { x: -80, y: 0, z: -60 }, // 起点：入口
                    { x: 0, y: 0, z: 0 },     // 矿坑湖
                    { x: 50, y: 20, z: 30 },  // 观景平台
                    { x: -30, y: 5, z: 20 },  // 生态步道
                    { x: 80, y: 10, z: -20 }  // 地质博物馆
                ],
                highlights: ['最佳摄影点', '地质知识学习', '生态观察'],
                tips: '建议上午开始游览，避开人流高峰'
            },
            {
                id: 'route_quick',
                name: '快速游览',
                description: '时间有限游客的精华路线',
                duration: 60,
                difficulty: 'easy',
                distance: 1.2,
                pois: ['poi_001', 'poi_002'],
                path: [
                    { x: -80, y: 0, z: -60 },
                    { x: 0, y: 0, z: 0 },
                    { x: 50, y: 20, z: 30 }
                ],
                highlights: ['核心景观', '最佳视角'],
                tips: '重点游览矿坑湖和观景平台'
            },
            {
                id: 'route_detailed',
                name: '深度游览',
                description: '包含所有景点的完整游览路线',
                duration: 180,
                difficulty: 'medium',
                distance: 3.8,
                pois: ['poi_001', 'poi_002', 'poi_003', 'poi_004', 'poi_005'],
                path: [
                    { x: -80, y: 0, z: -60 },
                    { x: 0, y: 0, z: 0 },
                    { x: 50, y: 20, z: 30 },
                    { x: -30, y: 5, z: 20 },
                    { x: 80, y: 10, z: -20 },
                    { x: -60, y: 0, z: -40 }
                ],
                highlights: ['全景体验', '深度了解', '亲子互动'],
                tips: '建议携带水和小食，中途可在休息点补充体力'
            }
        ];
    }

    // 加载宝藏数据
    async loadTreasureData() {
        this.treasures = [
            {
                id: 'treasure_001',
                name: '矿工的工具',
                description: '发现一把古老的矿工镐，见证了汤山的采矿历史',
                position: { x: 25, y: 2, z: 15 },
                type: 'historical',
                points: 100,
                difficulty: 'easy',
                hint: '在矿坑湖东侧的岩石缝隙中寻找',
                arMarker: 'treasure_marker_001',
                unlocked: false,
                riddle: '我曾在地下挥汗如雨，如今静静躺在湖边等你'
            },
            {
                id: 'treasure_002',
                name: '稀有矿物标本',
                description: '一块美丽的石英晶体，展现大自然的鬼斧神工',
                position: { x: -40, y: 8, z: -10 },
                type: 'geological',
                points: 150,
                difficulty: 'medium',
                hint: '在生态步道的第三个休息点附近',
                arMarker: 'treasure_marker_002',
                unlocked: false,
                riddle: '透明如水晶，坚硬如钢铁，我在绿荫下闪闪发光'
            },
            {
                id: 'treasure_003',
                name: '古代化石',
                description: '远古时期的植物化石，记录着地球的历史变迁',
                position: { x: 70, y: 15, z: 5 },
                type: 'fossil',
                points: 200,
                difficulty: 'hard',
                hint: '在观景平台下方的岩壁上',
                arMarker: 'treasure_marker_003',
                unlocked: false,
                riddle: '我是时间的见证者，在高处俯瞰着湖水'
            }
        ];
    }

    // 加载用户进度
    async loadUserProgress() {
        const saved = localStorage.getItem('tangshan_park_progress');
        if (saved) {
            this.userProgress = { ...this.userProgress, ...JSON.parse(saved) };
        }
    }

    // 保存用户进度
    saveUserProgress() {
        localStorage.setItem('tangshan_park_progress', JSON.stringify(this.userProgress));
    }

    // 获取景点信息
    getPOI(id) {
        return this.pointsOfInterest.find(poi => poi.id === id);
    }

    // 获取所有景点
    getAllPOIs() {
        return this.pointsOfInterest;
    }

    // 获取路线信息
    getRoute(id) {
        return this.routes.find(route => route.id === id);
    }

    // 获取所有路线
    getAllRoutes() {
        return this.routes;
    }

    // 获取宝藏信息
    getTreasure(id) {
        return this.treasures.find(treasure => treasure.id === id);
    }

    // 获取所有宝藏
    getAllTreasures() {
        return this.treasures;
    }

    // 标记景点为已访问
    markPOIVisited(poiId) {
        if (!this.userProgress.visitedPOIs.includes(poiId)) {
            this.userProgress.visitedPOIs.push(poiId);
            this.saveUserProgress();
        }
    }

    // 标记宝藏为已发现
    markTreasureFound(treasureId) {
        const treasure = this.getTreasure(treasureId);
        if (treasure && !this.userProgress.foundTreasures.includes(treasureId)) {
            this.userProgress.foundTreasures.push(treasureId);
            this.userProgress.totalScore += treasure.points;
            this.saveUserProgress();
            return treasure.points;
        }
        return 0;
    }

    // 标记路线为已完成
    markRouteCompleted(routeId) {
        if (!this.userProgress.completedRoutes.includes(routeId)) {
            this.userProgress.completedRoutes.push(routeId);
            this.userProgress.totalScore += 50; // 完成路线奖励
            this.saveUserProgress();
        }
    }

    // 获取用户进度
    getUserProgress() {
        return this.userProgress;
    }

    // 重置用户进度
    resetUserProgress() {
        this.userProgress = {
            visitedPOIs: [],
            foundTreasures: [],
            completedRoutes: [],
            totalScore: 0
        };
        this.saveUserProgress();
    }
}
