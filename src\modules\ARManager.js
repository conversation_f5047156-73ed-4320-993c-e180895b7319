// AR管理器 - 处理增强现实功能
export class ARManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.arScene = null;
        this.arCamera = null;
        this.isARSupported = false;
        this.isARActive = false;
        this.arSession = null;
        this.arMarkers = new Map();
        this.currentPOI = null;
        
        // AR相关元素
        this.arContainer = document.getElementById('ar-container');
        this.arSceneElement = document.getElementById('ar-scene');
        this.arControls = document.getElementById('ar-controls');
        
        this.isInitialized = false;
    }

    async init() {
        try {
            await this.checkARSupport();
            this.setupARScene();
            this.bindEvents();
            
            this.isInitialized = true;
            console.log('AR管理器初始化完成');
        } catch (error) {
            console.error('AR管理器初始化失败:', error);
            throw error;
        }
    }

    async checkARSupport() {
        // 检查WebXR支持
        if ('xr' in navigator) {
            try {
                const isSupported = await navigator.xr.isSessionSupported('immersive-ar');
                this.isARSupported = isSupported;
                console.log('WebXR AR支持:', isSupported);
            } catch (error) {
                console.log('WebXR不支持，回退到AR.js');
                this.isARSupported = true; // AR.js作为回退方案
            }
        } else {
            console.log('浏览器不支持WebXR，使用AR.js');
            this.isARSupported = true; // AR.js作为回退方案
        }

        // 检查相机权限
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ video: true });
            stream.getTracks().forEach(track => track.stop());
            console.log('相机权限获取成功');
        } catch (error) {
            console.warn('无法获取相机权限:', error);
            throw new Error('AR功能需要相机权限');
        }
    }

    setupARScene() {
        if (!this.arSceneElement) {
            console.error('AR场景元素未找到');
            return;
        }

        // 设置A-Frame场景属性
        this.arSceneElement.setAttribute('arjs', {
            sourceType: 'webcam',
            debugUIEnabled: false,
            detectionMode: 'mono_and_matrix',
            matrixCodeType: '3x3'
        });

        // 创建AR标记
        this.createARMarkers();
        
        // 设置AR相机
        this.setupARCamera();
    }

    createARMarkers() {
        const pois = this.dataManager.getAllPOIs();
        
        pois.forEach(poi => {
            // 为每个POI创建AR标记
            const marker = document.createElement('a-marker');
            marker.setAttribute('type', 'pattern');
            marker.setAttribute('url', `/assets/markers/${poi.id}.patt`);
            marker.setAttribute('id', `marker-${poi.id}`);
            
            // 创建3D内容
            const content = this.createARContent(poi);
            marker.appendChild(content);
            
            this.arSceneElement.appendChild(marker);
            this.arMarkers.set(poi.id, marker);
        });

        // 创建基于位置的AR标记（GPS）
        this.createLocationBasedMarkers();
    }

    createARContent(poi) {
        const group = document.createElement('a-entity');
        
        // 信息面板
        const infoPanel = document.createElement('a-plane');
        infoPanel.setAttribute('position', '0 1 0');
        infoPanel.setAttribute('width', '2');
        infoPanel.setAttribute('height', '1');
        infoPanel.setAttribute('color', 'white');
        infoPanel.setAttribute('opacity', '0.8');
        
        // 文字信息
        const text = document.createElement('a-text');
        text.setAttribute('value', poi.name);
        text.setAttribute('position', '0 1.2 0.01');
        text.setAttribute('align', 'center');
        text.setAttribute('color', 'black');
        text.setAttribute('width', '6');
        
        // 3D图标
        const icon = document.createElement('a-sphere');
        icon.setAttribute('position', '0 0.5 0');
        icon.setAttribute('radius', '0.2');
        icon.setAttribute('color', this.getPOIColor(poi.type));
        icon.setAttribute('animation', 'property: rotation; to: 0 360 0; loop: true; dur: 3000');
        
        group.appendChild(infoPanel);
        group.appendChild(text);
        group.appendChild(icon);
        
        // 添加点击事件
        group.setAttribute('cursor-listener', '');
        group.addEventListener('click', () => {
            this.onARMarkerClicked(poi);
        });
        
        return group;
    }

    createLocationBasedMarkers() {
        // 基于GPS位置的AR标记
        const locationMarkers = [
            { lat: 32.1234, lng: 118.5678, poiId: 'poi_001' },
            { lat: 32.1235, lng: 118.5679, poiId: 'poi_002' },
            // 更多位置标记...
        ];

        locationMarkers.forEach(location => {
            const marker = document.createElement('a-entity');
            marker.setAttribute('gps-entity-place', `latitude: ${location.lat}; longitude: ${location.lng}`);
            marker.setAttribute('look-at', '[gps-camera]');
            
            const poi = this.dataManager.getPOI(location.poiId);
            if (poi) {
                const content = this.createARContent(poi);
                marker.appendChild(content);
                this.arSceneElement.appendChild(marker);
            }
        });
    }

    setupARCamera() {
        const camera = document.getElementById('ar-camera');
        if (camera) {
            camera.setAttribute('gps-camera', 'gpsMinDistance: 5');
            camera.setAttribute('rotation-reader', '');
        }
    }

    bindEvents() {
        // AR控制按钮事件
        document.getElementById('ar-info-btn').addEventListener('click', () => {
            this.showARInfo();
        });

        document.getElementById('ar-route-btn').addEventListener('click', () => {
            this.showARRoute();
        });

        document.getElementById('ar-camera-btn').addEventListener('click', () => {
            this.takeARPhoto();
        });

        // 设备方向变化
        window.addEventListener('deviceorientationabsolute', (event) => {
            this.handleDeviceOrientation(event);
        });

        // AR场景事件
        this.arSceneElement.addEventListener('arjs-video-loaded', () => {
            console.log('AR视频流加载完成');
        });

        this.arSceneElement.addEventListener('markerFound', (event) => {
            this.onMarkerFound(event);
        });

        this.arSceneElement.addEventListener('markerLost', (event) => {
            this.onMarkerLost(event);
        });
    }

    getPOIColor(type) {
        const colors = {
            lake: '#4A90E2',
            viewpoint: '#F5A623',
            trail: '#7ED321',
            museum: '#9013FE',
            playground: '#FF6B6B'
        };
        return colors[type] || '#666666';
    }

    onARMarkerClicked(poi) {
        this.currentPOI = poi;
        
        // 显示POI详细信息
        window.dispatchEvent(new CustomEvent('arPOIClicked', {
            detail: { poi }
        }));

        // 播放音频导览
        if (poi.audioGuide) {
            this.playAudioGuide(poi.audioGuide);
        }

        // 标记为已访问
        this.dataManager.markPOIVisited(poi.id);
    }

    onMarkerFound(event) {
        console.log('发现AR标记:', event.target.id);
        
        // 显示标记相关信息
        const markerId = event.target.id.replace('marker-', '');
        const poi = this.dataManager.getPOI(markerId);
        
        if (poi) {
            this.showARNotification(`发现景点: ${poi.name}`);
        }
    }

    onMarkerLost(event) {
        console.log('丢失AR标记:', event.target.id);
    }

    showARInfo() {
        if (this.currentPOI) {
            // 显示当前POI的详细信息
            window.dispatchEvent(new CustomEvent('showPOIInfo', {
                detail: { poi: this.currentPOI }
            }));
        } else {
            this.showARNotification('请先扫描景点标记');
        }
    }

    showARRoute() {
        if (this.currentPOI) {
            // 显示到当前POI的路线
            window.dispatchEvent(new CustomEvent('planRouteToAR', {
                detail: { poiId: this.currentPOI.id }
            }));
        } else {
            this.showARNotification('请先选择目标景点');
        }
    }

    async takeARPhoto() {
        try {
            // 获取AR场景截图
            const canvas = this.arSceneElement.querySelector('canvas');
            if (canvas) {
                const dataURL = canvas.toDataURL('image/png');
                
                // 创建下载链接
                const link = document.createElement('a');
                link.download = `tangshan-ar-${Date.now()}.png`;
                link.href = dataURL;
                link.click();
                
                this.showARNotification('照片已保存');
            }
        } catch (error) {
            console.error('拍照失败:', error);
            this.showARNotification('拍照失败，请重试');
        }
    }

    playAudioGuide(audioUrl) {
        // 播放音频导览
        const audio = new Audio(audioUrl);
        audio.play().catch(error => {
            console.warn('音频播放失败:', error);
        });
    }

    showARNotification(message) {
        // 在AR界面显示通知
        const notification = document.createElement('div');
        notification.className = 'ar-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            z-index: 1000;
            font-size: 14px;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    handleDeviceOrientation(event) {
        // 处理设备方向变化，用于AR导航
        const alpha = event.alpha; // Z轴旋转
        const beta = event.beta;   // X轴旋转
        const gamma = event.gamma; // Y轴旋转
        
        // 更新AR相机方向
        if (this.arCamera) {
            this.arCamera.setAttribute('rotation', `${beta} ${alpha} ${gamma}`);
        }
    }

    focusOnPOI(poiId) {
        const poi = this.dataManager.getPOI(poiId);
        if (poi) {
            this.currentPOI = poi;
            
            // 如果在AR模式下，高亮显示对应的标记
            const marker = this.arMarkers.get(poiId);
            if (marker) {
                // 添加高亮效果
                const highlight = marker.querySelector('a-sphere');
                if (highlight) {
                    highlight.setAttribute('animation', 'property: scale; to: 1.5 1.5 1.5; loop: true; dir: alternate; dur: 1000');
                }
            }
        }
    }

    handleResize() {
        // AR场景通常会自动处理尺寸变化
        if (this.arSceneElement) {
            this.arSceneElement.resize();
        }
    }

    pause() {
        this.isARActive = false;
        
        // 停止AR会话
        if (this.arSession) {
            this.arSession.end();
        }
        
        // 停止视频流
        const video = this.arSceneElement.querySelector('video');
        if (video && video.srcObject) {
            video.srcObject.getTracks().forEach(track => track.stop());
        }
    }

    resume() {
        this.isARActive = true;
        
        // 重新启动AR
        if (this.isARSupported) {
            this.startAR();
        }
    }

    async startAR() {
        try {
            // 请求相机权限
            const stream = await navigator.mediaDevices.getUserMedia({ 
                video: { facingMode: 'environment' } 
            });
            
            // 启动AR场景
            if (this.arSceneElement) {
                this.arSceneElement.play();
            }
            
            console.log('AR启动成功');
        } catch (error) {
            console.error('AR启动失败:', error);
            this.showARNotification('AR启动失败，请检查相机权限');
        }
    }

    dispose() {
        this.pause();
        
        // 清理AR资源
        this.arMarkers.clear();
        
        if (this.arSceneElement) {
            this.arSceneElement.pause();
        }
    }
}
