#!/usr/bin/env python3
"""
简单的HTTP服务器，用于本地测试汤山矿坑公园三维导览系统
支持CORS和MIME类型，适合开发和测试使用
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头，允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # 添加安全头
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'SAMEORIGIN')
        
        super().end_headers()
    
    def guess_type(self, path):
        # 扩展MIME类型支持
        mimetype, encoding = super().guess_type(path)
        
        # 添加对现代Web文件类型的支持
        if path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.mjs'):
            return 'application/javascript'
        elif path.endswith('.json'):
            return 'application/json'
        elif path.endswith('.wasm'):
            return 'application/wasm'
        elif path.endswith('.glb'):
            return 'model/gltf-binary'
        elif path.endswith('.gltf'):
            return 'model/gltf+json'
        
        return mimetype
    
    def do_GET(self):
        # 处理SPA路由，将未找到的路径重定向到index.html
        parsed_path = urlparse(self.path)
        file_path = parsed_path.path.lstrip('/')
        
        # 如果请求的是根路径，显示目录列表
        if not file_path:
            return super().do_GET()
        
        # 检查文件是否存在
        if not os.path.exists(file_path) and not file_path.startswith('src/') and not file_path.startswith('public/'):
            # 如果是HTML请求且文件不存在，尝试返回index.html（SPA支持）
            if not '.' in os.path.basename(file_path):
                self.path = '/index.html'
        
        return super().do_GET()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[{self.date_time_string()}] {format % args}")

def run_server(port=8000):
    """启动HTTP服务器"""
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 汤山矿坑公园三维导览系统测试服务器启动")
            print(f"📍 服务地址: http://localhost:{port}")
            print(f"📁 服务目录: {os.getcwd()}")
            print(f"🌐 请在浏览器中访问以下地址进行测试:")
            print(f"   - 主应用: http://localhost:{port}/index.html")
            print(f"   - 演示页面: http://localhost:{port}/demo.html")
            print(f"   - 测试页面: http://localhost:{port}/test.html")
            print(f"⚠️  注意: AR功能需要HTTPS环境，本地测试可能受限")
            print(f"🛑 按 Ctrl+C 停止服务器")
            print("-" * 60)
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，请尝试其他端口")
            print(f"💡 使用命令: python simple-server.py {port + 1}")
        else:
            print(f"❌ 启动服务器失败: {e}")

if __name__ == "__main__":
    # 检查命令行参数
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 无效的端口号，使用默认端口 8000")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        sys.exit(1)
    
    # 启动服务器
    run_server(port)
