/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    height: 100vh;
}

#app {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.primary-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* 加载界面 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo h1 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #3498db, #2ecc71);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-logo p {
    font-size: 1.2rem;
    opacity: 0.8;
    margin-bottom: 2rem;
}

.loading-progress {
    width: 300px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
}

#loading-text {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* 主导航 */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-brand h2 {
    color: #2c3e50;
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    padding: 10px 20px;
    border: none;
    background: transparent;
    color: #666;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.nav-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.nav-btn.active {
    background: linear-gradient(45deg, #3498db, #2ecc71);
    color: white;
}

.nav-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    transform: scale(1.1);
}

/* 场景容器 */
.scene-container {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
}

#three-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* 场景控制面板 */
.scene-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    min-width: 200px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.control-group {
    margin-bottom: 20px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    color: #2c3e50;
    font-size: 14px;
}

.control-group button {
    display: block;
    width: 100%;
    margin-bottom: 8px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
}

.control-group button:hover {
    background: #f8f9fa;
    border-color: #3498db;
}

.control-group button:last-child {
    margin-bottom: 0;
}

/* AR容器 */
.ar-container {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
}

#ar-scene {
    width: 100%;
    height: 100%;
}

.ar-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.ar-btn {
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.ar-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

/* 路线规划界面 */
.route-container {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
}

.route-sidebar {
    width: 300px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px;
    overflow-y: auto;
}

.route-sidebar h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.route-type {
    margin-bottom: 20px;
}

.route-type label {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.route-type label:hover {
    background: rgba(52, 152, 219, 0.1);
}

.route-type input[type="radio"] {
    margin-right: 8px;
}

.poi-list h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.route-map {
    flex: 1;
    background: #000;
}

#route-3d-view {
    width: 100%;
    height: 100%;
}

/* 探宝游戏界面 */
.treasure-container {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
}

.treasure-hud {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.treasure-score {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    display: flex;
    gap: 20px;
    font-weight: bold;
    color: #2c3e50;
}

.treasure-hint {
    background: rgba(52, 152, 219, 0.9);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    max-width: 300px;
}

#treasure-ar-view {
    width: 100%;
    height: 100%;
    background: #000;
}

/* 信息面板 */
.info-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.info-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #666;
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.settings-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    width: 400px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.settings-content h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: bold;
}

.setting-group select,
.setting-group input[type="range"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 6px;
}

.setting-group input[type="checkbox"] {
    margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-nav {
        padding: 0 1rem;
    }
    
    .nav-brand h2 {
        font-size: 1.2rem;
    }
    
    .nav-menu {
        gap: 0.5rem;
    }
    
    .nav-btn {
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .route-container {
        flex-direction: column;
    }
    
    .route-sidebar {
        width: 100%;
        height: 200px;
    }
    
    .scene-controls {
        right: 10px;
        top: 10px;
        min-width: 150px;
        padding: 15px;
    }
    
    .treasure-hud {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .treasure-score {
        justify-content: center;
    }
}
