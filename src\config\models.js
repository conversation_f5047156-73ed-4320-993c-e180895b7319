// 汤山矿坑公园3D模型配置文件
export const MODEL_CONFIGS = {
    // 地形和环境模型
    terrain: {
        // 主要矿坑地形
        mainTerrain: {
            type: 'gltf',
            url: '/assets/models/terrain/tangshan_mine_terrain.glb',
            options: {
                scale: 1.0,
                position: { x: 0, y: 0, z: 0 },
                castShadow: true,
                receiveShadow: true,
                enableAO: true
            },
            priority: 'high',
            description: '汤山矿坑主要地形模型'
        },
        
        // 周边山体
        surroundingHills: {
            type: 'gltf',
            url: '/assets/models/terrain/surrounding_hills.glb',
            options: {
                scale: 1.0,
                position: { x: 0, y: 0, z: 0 },
                castShadow: true,
                receiveShadow: true
            },
            priority: 'medium',
            description: '周边山体地形'
        },
        
        // 湖底地形
        lakeBottom: {
            type: 'gltf',
            url: '/assets/models/terrain/lake_bottom.glb',
            options: {
                scale: 1.0,
                position: { x: 0, y: -15, z: 0 },
                receiveShadow: true
            },
            priority: 'low',
            description: '矿坑湖底地形'
        }
    },

    // 建筑和结构模型
    buildings: {
        // 观景平台
        viewingPlatform: {
            type: 'gltf',
            url: '/assets/models/buildings/viewing_platform.glb',
            options: {
                scale: 1.0,
                position: { x: 50, y: 20, z: 30 },
                rotation: { x: 0, y: 0, z: 0 },
                castShadow: true,
                receiveShadow: true
            },
            priority: 'high',
            description: '主观景平台建筑'
        },
        
        // 地质博物馆
        geologicalMuseum: {
            type: 'gltf',
            url: '/assets/models/buildings/geological_museum.glb',
            options: {
                scale: 1.0,
                position: { x: 80, y: 10, z: -20 },
                rotation: { x: 0, y: Math.PI / 4, z: 0 },
                castShadow: true,
                receiveShadow: true
            },
            priority: 'high',
            description: '地质博物馆建筑'
        },
        
        // 游客中心
        visitorCenter: {
            type: 'gltf',
            url: '/assets/models/buildings/visitor_center.glb',
            options: {
                scale: 1.0,
                position: { x: -80, y: 5, z: -60 },
                rotation: { x: 0, y: -Math.PI / 6, z: 0 },
                castShadow: true,
                receiveShadow: true
            },
            priority: 'medium',
            description: '游客服务中心'
        },
        
        // 儿童游乐设施
        playground: {
            type: 'gltf',
            url: '/assets/models/buildings/playground_equipment.glb',
            options: {
                scale: 1.0,
                position: { x: -60, y: 0, z: -40 },
                castShadow: true,
                receiveShadow: true
            },
            priority: 'medium',
            description: '儿童游乐设施'
        },
        
        // 休息亭
        restPavilions: [
            {
                type: 'gltf',
                url: '/assets/models/buildings/rest_pavilion.glb',
                options: {
                    scale: 0.8,
                    position: { x: 20, y: 5, z: 40 },
                    castShadow: true,
                    receiveShadow: true
                },
                priority: 'low',
                description: '休息亭1'
            },
            {
                type: 'gltf',
                url: '/assets/models/buildings/rest_pavilion.glb',
                options: {
                    scale: 0.8,
                    position: { x: -30, y: 8, z: 20 },
                    rotation: { x: 0, y: Math.PI / 3, z: 0 },
                    castShadow: true,
                    receiveShadow: true
                },
                priority: 'low',
                description: '休息亭2'
            }
        ]
    },

    // 植被模型
    vegetation: {
        // 大型树木
        largeTrees: {
            type: 'gltf',
            url: '/assets/models/vegetation/large_trees_pack.glb',
            options: {
                scale: 1.0,
                castShadow: true,
                enableInstancing: true // 启用实例化渲染
            },
            instances: [
                { position: { x: 60, y: 0, z: 40 }, rotation: { x: 0, y: 0.5, z: 0 }, scale: 1.2 },
                { position: { x: -40, y: 0, z: 60 }, rotation: { x: 0, y: 1.2, z: 0 }, scale: 0.9 },
                { position: { x: 70, y: 0, z: -30 }, rotation: { x: 0, y: 2.1, z: 0 }, scale: 1.1 },
                { position: { x: -70, y: 0, z: -50 }, rotation: { x: 0, y: 0.8, z: 0 }, scale: 1.0 }
            ],
            priority: 'medium',
            description: '大型景观树木'
        },
        
        // 灌木丛
        bushes: {
            type: 'gltf',
            url: '/assets/models/vegetation/bushes_pack.glb',
            options: {
                scale: 1.0,
                enableInstancing: true
            },
            instances: [
                { position: { x: 30, y: 0, z: 25 }, scale: 0.8 },
                { position: { x: -25, y: 0, z: 35 }, scale: 1.1 },
                { position: { x: 45, y: 0, z: -15 }, scale: 0.9 }
            ],
            priority: 'low',
            description: '灌木丛植被'
        },
        
        // 水生植物
        aquaticPlants: {
            type: 'gltf',
            url: '/assets/models/vegetation/aquatic_plants.glb',
            options: {
                scale: 1.0,
                position: { x: 0, y: -12, z: 0 },
                enableInstancing: true
            },
            priority: 'low',
            description: '湖边水生植物'
        }
    },

    // 装饰和细节模型
    details: {
        // 路灯
        streetLights: {
            type: 'gltf',
            url: '/assets/models/details/street_light.glb',
            options: {
                scale: 1.0,
                castShadow: true,
                enableInstancing: true
            },
            instances: [
                { position: { x: 40, y: 0, z: 20 } },
                { position: { x: -20, y: 0, z: 30 } },
                { position: { x: 60, y: 0, z: -10 } },
                { position: { x: -50, y: 0, z: -20 } }
            ],
            priority: 'low',
            description: '园区路灯'
        },
        
        // 指示牌
        signBoards: {
            type: 'gltf',
            url: '/assets/models/details/sign_boards.glb',
            options: {
                scale: 1.0,
                castShadow: true
            },
            instances: [
                { position: { x: 25, y: 0, z: 15 }, rotation: { x: 0, y: 0.5, z: 0 } },
                { position: { x: -15, y: 0, z: 25 }, rotation: { x: 0, y: -0.3, z: 0 } }
            ],
            priority: 'medium',
            description: '导览指示牌'
        },
        
        // 座椅
        benches: {
            type: 'gltf',
            url: '/assets/models/details/park_bench.glb',
            options: {
                scale: 1.0,
                castShadow: true,
                receiveShadow: true,
                enableInstancing: true
            },
            instances: [
                { position: { x: 35, y: 0, z: 25 }, rotation: { x: 0, y: 0.8, z: 0 } },
                { position: { x: -25, y: 0, z: 15 }, rotation: { x: 0, y: -0.5, z: 0 } },
                { position: { x: 55, y: 20, z: 35 }, rotation: { x: 0, y: 1.2, z: 0 } }
            ],
            priority: 'low',
            description: '休息座椅'
        },
        
        // 垃圾桶
        trashBins: {
            type: 'gltf',
            url: '/assets/models/details/trash_bin.glb',
            options: {
                scale: 1.0,
                castShadow: true,
                enableInstancing: true
            },
            instances: [
                { position: { x: 30, y: 0, z: 20 } },
                { position: { x: -20, y: 0, z: 25 } },
                { position: { x: 50, y: 20, z: 30 } }
            ],
            priority: 'low',
            description: '垃圾桶'
        }
    },

    // 特殊效果模型
    effects: {
        // 水花效果
        waterSplash: {
            type: 'gltf',
            url: '/assets/models/effects/water_splash.glb',
            options: {
                scale: 1.0,
                position: { x: 0, y: -10, z: 0 },
                transparent: true,
                opacity: 0.7
            },
            priority: 'low',
            description: '水面波纹效果'
        },
        
        // 鸟群动画
        birds: {
            type: 'gltf',
            url: '/assets/models/effects/flying_birds.glb',
            options: {
                scale: 1.0,
                position: { x: 0, y: 50, z: 0 },
                enableAnimation: true
            },
            priority: 'low',
            description: '飞鸟动画效果'
        }
    }
};

// 模型加载优先级配置
export const LOADING_PRIORITIES = {
    high: 1,    // 核心模型，优先加载
    medium: 2,  // 重要模型，次要加载
    low: 3      // 装饰模型，最后加载
};

// 性能配置
export const PERFORMANCE_CONFIGS = {
    // 高性能设备配置
    high: {
        maxTriangles: 1000000,
        maxTextures: 100,
        enableLOD: true,
        enableInstancing: true,
        enableOcclusion: true,
        shadowMapSize: 2048,
        anisotropy: 16
    },
    
    // 中等性能设备配置
    medium: {
        maxTriangles: 500000,
        maxTextures: 50,
        enableLOD: true,
        enableInstancing: true,
        enableOcclusion: false,
        shadowMapSize: 1024,
        anisotropy: 8
    },
    
    // 低性能设备配置
    low: {
        maxTriangles: 200000,
        maxTextures: 25,
        enableLOD: true,
        enableInstancing: false,
        enableOcclusion: false,
        shadowMapSize: 512,
        anisotropy: 4
    }
};

// 获取扁平化的模型列表
export function getFlatModelList() {
    const models = [];
    
    function addModels(obj, category) {
        for (const [key, value] of Object.entries(obj)) {
            if (Array.isArray(value)) {
                value.forEach((item, index) => {
                    models.push({
                        id: `${category}_${key}_${index}`,
                        category,
                        name: key,
                        ...item
                    });
                });
            } else if (value.instances) {
                // 处理实例化模型
                models.push({
                    id: `${category}_${key}`,
                    category,
                    name: key,
                    ...value
                });
            } else if (value.type) {
                models.push({
                    id: `${category}_${key}`,
                    category,
                    name: key,
                    ...value
                });
            } else {
                addModels(value, `${category}_${key}`);
            }
        }
    }
    
    addModels(MODEL_CONFIGS, '');
    return models;
}

// 根据优先级获取模型列表
export function getModelsByPriority(priority) {
    const allModels = getFlatModelList();
    return allModels.filter(model => model.priority === priority);
}

// 获取所有高优先级模型
export function getHighPriorityModels() {
    return getModelsByPriority('high');
}

// 获取所有中优先级模型
export function getMediumPriorityModels() {
    return getModelsByPriority('medium');
}

// 获取所有低优先级模型
export function getLowPriorityModels() {
    return getModelsByPriority('low');
}
