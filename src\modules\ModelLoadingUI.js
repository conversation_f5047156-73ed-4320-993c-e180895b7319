// 模型加载界面管理器 - 处理3D模型加载的用户界面
export class ModelLoadingUI {
    constructor() {
        this.loadingPanel = null;
        this.progressBar = null;
        this.statusText = null;
        this.modelList = null;
        this.performanceInfo = null;
        
        this.isVisible = false;
        this.loadingStats = {
            totalModels: 0,
            loadedModels: 0,
            failedModels: 0,
            currentModel: ''
        };
        
        this.createUI();
        this.bindEvents();
    }

    createUI() {
        // 创建主面板
        this.loadingPanel = document.createElement('div');
        this.loadingPanel.className = 'model-loading-panel';
        this.loadingPanel.innerHTML = `
            <div class="loading-header">
                <h3>🏗️ 3D模型加载管理</h3>
                <button class="close-btn" id="close-model-loading">×</button>
            </div>
            
            <div class="loading-content">
                <div class="loading-progress">
                    <div class="progress-info">
                        <span id="loading-status">准备加载模型...</span>
                        <span id="loading-percentage">0%</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="model-progress-bar"></div>
                    </div>
                    <div class="loading-details">
                        <span id="current-model">等待开始...</span>
                    </div>
                </div>
                
                <div class="performance-section">
                    <h4>📊 性能信息</h4>
                    <div class="performance-grid">
                        <div class="perf-item">
                            <label>FPS:</label>
                            <span id="fps-display">--</span>
                        </div>
                        <div class="perf-item">
                            <label>三角形:</label>
                            <span id="triangles-display">--</span>
                        </div>
                        <div class="perf-item">
                            <label>模型数:</label>
                            <span id="models-display">--</span>
                        </div>
                        <div class="perf-item">
                            <label>性能级别:</label>
                            <select id="performance-level">
                                <option value="high">高</option>
                                <option value="medium" selected>中</option>
                                <option value="low">低</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="model-management">
                    <h4>🎯 模型管理</h4>
                    <div class="model-controls">
                        <button class="control-btn" id="reload-all-models">重新加载全部</button>
                        <button class="control-btn" id="toggle-fallback-models">切换备用模型</button>
                        <button class="control-btn" id="clear-model-cache">清理缓存</button>
                    </div>
                    
                    <div class="category-controls">
                        <h5>按类别控制:</h5>
                        <div class="category-buttons">
                            <button class="category-btn" data-category="terrain">地形</button>
                            <button class="category-btn" data-category="buildings">建筑</button>
                            <button class="category-btn" data-category="vegetation">植被</button>
                            <button class="category-btn" data-category="details">细节</button>
                        </div>
                    </div>
                    
                    <div class="model-list-container">
                        <h5>模型列表:</h5>
                        <div class="model-list" id="model-list"></div>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addStyles();
        
        // 添加到页面
        document.body.appendChild(this.loadingPanel);
        
        // 获取元素引用
        this.progressBar = document.getElementById('model-progress-bar');
        this.statusText = document.getElementById('loading-status');
        this.modelList = document.getElementById('model-list');
        this.performanceInfo = {
            fps: document.getElementById('fps-display'),
            triangles: document.getElementById('triangles-display'),
            models: document.getElementById('models-display')
        };
        
        // 默认隐藏
        this.hide();
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .model-loading-panel {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 600px;
                max-height: 80vh;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                overflow: hidden;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            .loading-header {
                background: linear-gradient(45deg, #3498db, #2ecc71);
                color: white;
                padding: 15px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .loading-header h3 {
                margin: 0;
                font-size: 1.2rem;
            }
            
            .close-btn {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background 0.3s ease;
            }
            
            .close-btn:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            
            .loading-content {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }
            
            .loading-progress {
                margin-bottom: 25px;
            }
            
            .progress-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            .progress-bar-container {
                width: 100%;
                height: 8px;
                background: #ecf0f1;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 10px;
            }
            
            .progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #3498db, #2ecc71);
                width: 0%;
                transition: width 0.3s ease;
                border-radius: 4px;
            }
            
            .loading-details {
                font-size: 0.9rem;
                color: #666;
                font-style: italic;
            }
            
            .performance-section {
                margin-bottom: 25px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
            }
            
            .performance-section h4 {
                margin: 0 0 15px 0;
                color: #2c3e50;
            }
            
            .performance-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .perf-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px;
                background: white;
                border-radius: 6px;
                border: 1px solid #dee2e6;
            }
            
            .perf-item label {
                font-weight: bold;
                color: #495057;
            }
            
            .perf-item span {
                color: #007bff;
                font-weight: bold;
            }
            
            .perf-item select {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 4px 8px;
                background: white;
            }
            
            .model-management h4 {
                margin: 0 0 15px 0;
                color: #2c3e50;
            }
            
            .model-controls {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }
            
            .control-btn {
                padding: 8px 16px;
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: background 0.3s ease;
            }
            
            .control-btn:hover {
                background: #5a6268;
            }
            
            .category-controls {
                margin-bottom: 15px;
            }
            
            .category-controls h5 {
                margin: 0 0 10px 0;
                color: #495057;
            }
            
            .category-buttons {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }
            
            .category-btn {
                padding: 6px 12px;
                background: #e9ecef;
                color: #495057;
                border: 1px solid #ced4da;
                border-radius: 20px;
                cursor: pointer;
                font-size: 0.8rem;
                transition: all 0.3s ease;
            }
            
            .category-btn:hover {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }
            
            .category-btn.active {
                background: #28a745;
                color: white;
                border-color: #28a745;
            }
            
            .model-list-container h5 {
                margin: 0 0 10px 0;
                color: #495057;
            }
            
            .model-list {
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background: white;
            }
            
            .model-item {
                padding: 10px;
                border-bottom: 1px solid #f1f3f4;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .model-item:last-child {
                border-bottom: none;
            }
            
            .model-info {
                flex: 1;
            }
            
            .model-name {
                font-weight: bold;
                color: #2c3e50;
                font-size: 0.9rem;
            }
            
            .model-details {
                font-size: 0.8rem;
                color: #666;
                margin-top: 2px;
            }
            
            .model-status {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
            }
            
            .status-loaded { background: #28a745; }
            .status-loading { background: #ffc107; }
            .status-failed { background: #dc3545; }
            .status-fallback { background: #6c757d; }
            
            .model-toggle {
                background: none;
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 4px 8px;
                cursor: pointer;
                font-size: 0.8rem;
                transition: all 0.3s ease;
            }
            
            .model-toggle:hover {
                background: #f8f9fa;
            }
            
            .model-toggle.visible {
                background: #28a745;
                color: white;
                border-color: #28a745;
            }
        `;
        document.head.appendChild(style);
    }

    bindEvents() {
        // 关闭按钮
        document.getElementById('close-model-loading').addEventListener('click', () => {
            this.hide();
        });

        // 性能级别选择
        document.getElementById('performance-level').addEventListener('change', (e) => {
            this.onPerformanceLevelChanged(e.target.value);
        });

        // 控制按钮
        document.getElementById('reload-all-models').addEventListener('click', () => {
            this.onReloadAllModels();
        });

        document.getElementById('toggle-fallback-models').addEventListener('click', () => {
            this.onToggleFallbackModels();
        });

        document.getElementById('clear-model-cache').addEventListener('click', () => {
            this.onClearModelCache();
        });

        // 类别按钮
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.onCategoryToggle(e.target.dataset.category);
            });
        });

        // 监听模型加载进度事件
        window.addEventListener('modelLoadingProgress', (e) => {
            this.updateProgress(e.detail);
        });

        // 点击面板外部关闭
        this.loadingPanel.addEventListener('click', (e) => {
            if (e.target === this.loadingPanel) {
                this.hide();
            }
        });
    }

    show() {
        this.loadingPanel.style.display = 'block';
        this.isVisible = true;
        this.updateModelList();
    }

    hide() {
        this.loadingPanel.style.display = 'none';
        this.isVisible = false;
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    updateProgress(progressData) {
        const { progress, loaded, total } = progressData;
        
        this.loadingStats.totalModels = total;
        this.loadingStats.loadedModels = loaded;
        
        // 更新进度条
        this.progressBar.style.width = `${progress}%`;
        
        // 更新状态文本
        document.getElementById('loading-percentage').textContent = `${Math.round(progress)}%`;
        document.getElementById('loading-status').textContent = 
            `已加载 ${loaded}/${total} 个模型`;
    }

    updatePerformanceInfo(stats) {
        if (this.performanceInfo.fps) {
            this.performanceInfo.fps.textContent = stats.fps || '--';
        }
        if (this.performanceInfo.triangles) {
            this.performanceInfo.triangles.textContent = 
                stats.totalTriangles ? stats.totalTriangles.toLocaleString() : '--';
        }
        if (this.performanceInfo.models) {
            this.performanceInfo.models.textContent = 
                `${stats.loadedModels || 0}/${stats.totalModels || 0}`;
        }
    }

    updateModelList() {
        // 这个方法需要从SceneManager获取模型列表
        // 实际实现时需要传入模型数据
        if (window.TangshanParkApp && window.TangshanParkApp.getManager) {
            const sceneManager = window.TangshanParkApp.getManager('scene');
            if (sceneManager && sceneManager.getLoadedModels) {
                const models = sceneManager.getLoadedModels();
                this.renderModelList(models);
            }
        }
    }

    renderModelList(models) {
        this.modelList.innerHTML = '';
        
        models.forEach(model => {
            const modelItem = document.createElement('div');
            modelItem.className = 'model-item';
            
            const statusClass = model.isFallback ? 'status-fallback' : 'status-loaded';
            const statusText = model.isFallback ? '备用' : '已加载';
            
            modelItem.innerHTML = `
                <div class="model-info">
                    <div class="model-name">${model.id}</div>
                    <div class="model-details">
                        ${model.description} | 
                        ${model.info ? `${model.info.triangles} 三角形` : ''}
                    </div>
                </div>
                <div class="model-status">
                    <div class="status-indicator ${statusClass}"></div>
                    <span>${statusText}</span>
                    <button class="model-toggle ${model.visible ? 'visible' : ''}" 
                            data-model-id="${model.id}">
                        ${model.visible ? '隐藏' : '显示'}
                    </button>
                </div>
            `;
            
            // 绑定切换按钮事件
            const toggleBtn = modelItem.querySelector('.model-toggle');
            toggleBtn.addEventListener('click', () => {
                this.onModelToggle(model.id, !model.visible);
            });
            
            this.modelList.appendChild(modelItem);
        });
    }

    // 事件处理方法
    onPerformanceLevelChanged(level) {
        if (window.TangshanParkApp && window.TangshanParkApp.getManager) {
            const sceneManager = window.TangshanParkApp.getManager('scene');
            if (sceneManager && sceneManager.setPerformanceLevel) {
                sceneManager.setPerformanceLevel(level);
            }
        }
    }

    onReloadAllModels() {
        console.log('重新加载所有模型');
        // 实现重新加载逻辑
    }

    onToggleFallbackModels() {
        console.log('切换备用模型显示');
        // 实现备用模型切换逻辑
    }

    onClearModelCache() {
        console.log('清理模型缓存');
        if (window.TangshanParkApp && window.TangshanParkApp.getManager) {
            const sceneManager = window.TangshanParkApp.getManager('scene');
            if (sceneManager && sceneManager.modelManager) {
                sceneManager.modelManager.clearCache();
            }
        }
    }

    onCategoryToggle(category) {
        console.log(`切换类别: ${category}`);
        if (window.TangshanParkApp && window.TangshanParkApp.getManager) {
            const sceneManager = window.TangshanParkApp.getManager('scene');
            if (sceneManager && sceneManager.toggleCategoryVisibility) {
                sceneManager.toggleCategoryVisibility(category);
                this.updateModelList();
            }
        }
    }

    onModelToggle(modelId, visible) {
        console.log(`切换模型 ${modelId} 可见性: ${visible}`);
        if (window.TangshanParkApp && window.TangshanParkApp.getManager) {
            const sceneManager = window.TangshanParkApp.getManager('scene');
            if (sceneManager && sceneManager.toggleModelVisibility) {
                sceneManager.toggleModelVisibility(modelId, visible);
                this.updateModelList();
            }
        }
    }

    dispose() {
        if (this.loadingPanel && this.loadingPanel.parentNode) {
            this.loadingPanel.parentNode.removeChild(this.loadingPanel);
        }
    }
}
