# 部署指南

本文档详细说明如何部署汤山矿坑公园三维导览系统到生产环境。

## 📋 部署前准备

### 系统要求
- **Node.js**: 16.0 或更高版本
- **npm**: 8.0 或更高版本
- **服务器**: 支持HTTPS的Web服务器
- **域名**: 建议使用HTTPS域名（AR功能必需）

### 环境检查
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查Git版本
git --version
```

## 🚀 快速部署

### 1. 获取源代码
```bash
# 克隆项目
git clone [repository-url]
cd tangshan-mine-park-ar-guide

# 或者下载ZIP包并解压
```

### 2. 安装依赖
```bash
# 安装项目依赖
npm install

# 如果遇到网络问题，可以使用国内镜像
npm install --registry=https://registry.npmmirror.com
```

### 3. 构建生产版本
```bash
# 构建生产版本
npm run build

# 构建完成后，dist目录包含所有静态文件
```

### 4. 部署到服务器
```bash
# 将dist目录内容上传到Web服务器
# 例如使用rsync
rsync -avz dist/ user@server:/var/www/html/

# 或使用FTP/SFTP工具上传
```

## 🌐 服务器配置

### Nginx配置示例
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 网站根目录
    root /var/www/html;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # AR功能需要的权限策略
    add_header Permissions-Policy "camera=*, microphone=*, geolocation=*" always;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### Apache配置示例
```apache
<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /var/www/html
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # 静态文件缓存
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
    
    # SPA路由支持
    <Directory "/var/www/html">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # 安全头设置
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Permissions-Policy "camera=*, microphone=*, geolocation=*"
</VirtualHost>
```

## ☁️ 云平台部署

### Vercel部署
1. 在Vercel官网注册账号
2. 连接GitHub仓库
3. 配置构建设置：
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "installCommand": "npm install"
   }
   ```
4. 部署完成后自动获得HTTPS域名

### Netlify部署
1. 在Netlify官网注册账号
2. 拖拽dist文件夹到部署区域
3. 或连接Git仓库自动部署
4. 配置重定向规则：
   ```
   /*    /index.html   200
   ```

### GitHub Pages部署
```bash
# 安装gh-pages
npm install --save-dev gh-pages

# 添加部署脚本到package.json
"scripts": {
  "deploy": "gh-pages -d dist"
}

# 部署
npm run build
npm run deploy
```

## 🔧 环境变量配置

创建`.env.production`文件：
```env
# API配置
VITE_API_BASE_URL=https://api.your-domain.com
VITE_CDN_BASE_URL=https://cdn.your-domain.com

# 功能开关
VITE_ENABLE_AR=true
VITE_ENABLE_ANALYTICS=true

# 第三方服务
VITE_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
VITE_SENTRY_DSN=YOUR_SENTRY_DSN
```

## 📊 性能优化

### 1. 资源优化
```bash
# 图片压缩
npm install --save-dev imagemin imagemin-webp

# 代码分割已在vite.config.js中配置
# 查看构建分析
npm run build -- --analyze
```

### 2. CDN配置
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      external: ['three', 'ar.js'],
      output: {
        globals: {
          'three': 'THREE',
          'ar.js': 'ARJS'
        }
      }
    }
  }
})
```

### 3. 缓存策略
- 静态资源：1年缓存
- HTML文件：不缓存或短期缓存
- API响应：根据数据更新频率设置

## 🔍 监控和分析

### 1. 错误监控
```javascript
// 集成Sentry
import * as Sentry from "@sentry/browser";

Sentry.init({
  dsn: "YOUR_SENTRY_DSN",
  environment: "production"
});
```

### 2. 性能监控
```javascript
// 集成Google Analytics
gtag('config', 'GA_MEASUREMENT_ID', {
  page_title: 'Tangshan Mine Park AR Guide',
  page_location: window.location.href
});
```

### 3. 用户行为分析
- 页面访问统计
- 功能使用情况
- AR功能成功率
- 用户停留时间

## 🔒 安全配置

### 1. HTTPS配置
- 使用Let's Encrypt免费SSL证书
- 配置HSTS头
- 启用HTTP/2

### 2. 内容安全策略
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' 'unsafe-eval'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: blob:; 
               media-src 'self' blob:;">
```

### 3. 权限策略
```html
<meta http-equiv="Permissions-Policy" 
      content="camera=*, microphone=*, geolocation=*">
```

## 🧪 部署测试

### 1. 功能测试清单
- [ ] 页面正常加载
- [ ] 3D场景渲染正常
- [ ] AR功能可用（需HTTPS）
- [ ] 路线规划功能正常
- [ ] 探宝游戏可玩
- [ ] 移动端适配良好
- [ ] 跨浏览器兼容

### 2. 性能测试
```bash
# 使用Lighthouse测试
npm install -g lighthouse
lighthouse https://your-domain.com --output html --output-path ./lighthouse-report.html
```

### 3. 兼容性测试
- Chrome 90+
- Safari 14+
- Firefox 88+
- Edge 90+
- 移动端浏览器

## 🚨 故障排除

### 常见问题

1. **AR功能无法使用**
   - 检查是否使用HTTPS
   - 确认相机权限已授予
   - 检查浏览器兼容性

2. **3D场景加载缓慢**
   - 优化模型文件大小
   - 启用CDN加速
   - 检查网络连接

3. **移动端性能问题**
   - 降低渲染质量
   - 减少同时渲染的对象
   - 优化纹理分辨率

### 日志查看
```bash
# 查看服务器日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 查看应用日志
console.log() # 浏览器开发者工具
```

## 📞 技术支持

如遇到部署问题，请：
1. 查看本文档的故障排除部分
2. 检查浏览器控制台错误信息
3. 提交Issue到项目仓库
4. 联系技术支持团队

---

**部署成功后，您的汤山矿坑公园三维导览系统就可以为游客提供服务了！** 🎉
