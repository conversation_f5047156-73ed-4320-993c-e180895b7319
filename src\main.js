// 汤山矿坑公园三维导览系统 - 主入口文件
import { SceneManager } from './modules/SceneManager.js';
import { ARManager } from './modules/ARManager.js';
import { RouteManager } from './modules/RouteManager.js';
import { TreasureManager } from './modules/TreasureManager.js';
import { UIManager } from './modules/UIManager.js';
import { DataManager } from './modules/DataManager.js';
import { LoadingManager } from './modules/LoadingManager.js';
import { ModelLoadingUI } from './modules/ModelLoadingUI.js';

class TangshanParkApp {
    constructor() {
        this.currentMode = '3d';
        this.managers = {};
        this.modelLoadingUI = null;
        this.isInitialized = false;

        this.init();
    }

    async init() {
        try {
            // 显示加载界面
            this.loadingManager = new LoadingManager();
            this.loadingManager.show();

            // 初始化数据管理器
            this.managers.data = new DataManager();
            await this.managers.data.init();
            this.loadingManager.updateProgress(20, '加载景点数据...');

            // 初始化UI管理器
            this.managers.ui = new UIManager();
            this.managers.ui.init();
            this.loadingManager.updateProgress(40, '初始化界面...');

            // 初始化3D场景管理器
            this.managers.scene = new SceneManager();
            await this.managers.scene.init();
            this.loadingManager.updateProgress(60, '加载3D模型...');

            // 初始化路线管理器
            this.managers.route = new RouteManager(this.managers.scene, this.managers.data);
            await this.managers.route.init();
            this.loadingManager.updateProgress(70, '初始化路线系统...');

            // 初始化AR管理器
            this.managers.ar = new ARManager(this.managers.data);
            await this.managers.ar.init();
            this.loadingManager.updateProgress(85, '初始化AR系统...');

            // 初始化探宝游戏管理器
            this.managers.treasure = new TreasureManager(this.managers.ar, this.managers.data);
            await this.managers.treasure.init();
            this.loadingManager.updateProgress(90, '初始化游戏系统...');

            // 初始化模型加载界面
            this.modelLoadingUI = new ModelLoadingUI();
            this.loadingManager.updateProgress(95, '初始化模型管理界面...');

            // 绑定事件
            this.bindEvents();
            this.loadingManager.updateProgress(100, '加载完成！');

            // 延迟隐藏加载界面
            setTimeout(() => {
                this.loadingManager.hide();
                this.showMode('3d');
                this.isInitialized = true;
            }, 1000);

        } catch (error) {
            console.error('应用初始化失败:', error);
            this.loadingManager.showError('系统初始化失败，请刷新页面重试');
        }
    }

    bindEvents() {
        // 导航按钮事件
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mode = e.target.dataset.mode;
                this.switchMode(mode);
            });
        });

        // 设置按钮事件
        document.getElementById('settings-btn').addEventListener('click', () => {
            this.managers.ui.showSettings();
        });

        // 帮助按钮事件
        document.getElementById('help-btn').addEventListener('click', () => {
            this.managers.ui.showHelp();
        });

        // 添加模型管理按钮（如果不存在则创建）
        this.addModelManagementButton();

        // 窗口大小变化事件
        window.addEventListener('resize', () => {
            if (this.isInitialized) {
                this.managers.scene.handleResize();
                if (this.currentMode === 'ar') {
                    this.managers.ar.handleResize();
                }
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });

        // 移动设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                if (this.isInitialized) {
                    this.managers.scene.handleResize();
                    if (this.currentMode === 'ar') {
                        this.managers.ar.handleResize();
                    }
                }
            }, 100);
        });
    }

    switchMode(mode) {
        if (!this.isInitialized || mode === this.currentMode) return;

        // 隐藏当前模式
        this.hideMode(this.currentMode);
        
        // 显示新模式
        this.showMode(mode);
        
        // 更新导航状态
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.mode === mode) {
                btn.classList.add('active');
            }
        });

        this.currentMode = mode;
    }

    hideMode(mode) {
        switch (mode) {
            case '3d':
                document.getElementById('scene-container').classList.add('hidden');
                this.managers.scene.pause();
                break;
            case 'route':
                document.getElementById('route-container').classList.add('hidden');
                this.managers.route.pause();
                break;
            case 'ar':
                document.getElementById('ar-container').classList.add('hidden');
                this.managers.ar.pause();
                break;
            case 'treasure':
                document.getElementById('treasure-container').classList.add('hidden');
                this.managers.treasure.pause();
                break;
        }
    }

    showMode(mode) {
        switch (mode) {
            case '3d':
                document.getElementById('scene-container').classList.remove('hidden');
                this.managers.scene.resume();
                break;
            case 'route':
                document.getElementById('route-container').classList.remove('hidden');
                this.managers.route.resume();
                break;
            case 'ar':
                document.getElementById('ar-container').classList.remove('hidden');
                this.managers.ar.resume();
                break;
            case 'treasure':
                document.getElementById('treasure-container').classList.remove('hidden');
                this.managers.treasure.resume();
                break;
        }
    }

    handleKeyboard(e) {
        if (!this.isInitialized) return;

        switch (e.key) {
            case 'Escape':
                this.managers.ui.closeAllPanels();
                break;
            case 'h':
            case 'H':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.managers.ui.showHelp();
                }
                break;
            case 'm':
            case 'M':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.toggleModelManagement();
                }
                break;
            case 'r':
            case 'R':
                if (this.currentMode === '3d') {
                    this.managers.scene.resetCamera();
                }
                break;
            case '1':
                this.switchMode('3d');
                break;
            case '2':
                this.switchMode('route');
                break;
            case '3':
                this.switchMode('ar');
                break;
            case '4':
                this.switchMode('treasure');
                break;
        }
    }

    // 公共方法供其他模块调用
    showInfo(title, content) {
        this.managers.ui.showInfo(title, content);
    }

    showNotification(message, type = 'info') {
        this.managers.ui.showNotification(message, type);
    }

    // 获取管理器实例
    getManager(name) {
        return this.managers[name];
    }

    // 添加模型管理按钮
    addModelManagementButton() {
        const navControls = document.querySelector('.nav-controls');
        if (navControls && !document.getElementById('model-mgmt-btn')) {
            const modelBtn = document.createElement('button');
            modelBtn.id = 'model-mgmt-btn';
            modelBtn.className = 'control-btn';
            modelBtn.innerHTML = '🏗️';
            modelBtn.title = '3D模型管理 (Ctrl+M)';
            modelBtn.addEventListener('click', () => {
                this.toggleModelManagement();
            });
            navControls.insertBefore(modelBtn, navControls.firstChild);
        }
    }

    // 切换模型管理界面
    toggleModelManagement() {
        if (this.modelLoadingUI) {
            this.modelLoadingUI.toggle();

            // 更新性能信息
            if (this.modelLoadingUI.isVisible && this.managers.scene) {
                const stats = this.managers.scene.getSceneStats();
                this.modelLoadingUI.updatePerformanceInfo(stats);

                // 定期更新性能信息
                this.startPerformanceMonitoring();
            }
        }
    }

    // 开始性能监控
    startPerformanceMonitoring() {
        if (this.performanceInterval) {
            clearInterval(this.performanceInterval);
        }

        this.performanceInterval = setInterval(() => {
            if (this.modelLoadingUI && this.modelLoadingUI.isVisible && this.managers.scene) {
                const stats = this.managers.scene.getSceneStats();
                this.modelLoadingUI.updatePerformanceInfo(stats);
            } else {
                clearInterval(this.performanceInterval);
                this.performanceInterval = null;
            }
        }, 1000);
    }

    // 获取系统状态
    getSystemStatus() {
        if (!this.isInitialized) {
            return { status: 'initializing' };
        }

        const status = {
            status: 'ready',
            currentMode: this.currentMode,
            managers: Object.keys(this.managers),
            modelLoadingUI: !!this.modelLoadingUI
        };

        if (this.managers.scene) {
            status.sceneStats = this.managers.scene.getSceneStats();
        }

        return status;
    }
}

// 全局应用实例
let app;

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app = new TangshanParkApp();
    
    // 将应用实例暴露到全局，方便调试
    window.TangshanParkApp = app;
});

// 导出应用类
export { TangshanParkApp };
