# 汤山矿坑公园三维导览系统

一个基于Web技术的三维AR导览系统，为汤山矿坑公园提供沉浸式的游览体验。

## 🌟 功能特色

### 🎯 核心功能
- **三维模型展示** - 使用Three.js渲染的高质量3D场景
- **智能路线规划** - 多种预设路线和自定义路线规划
- **AR增强现实导览** - 基于WebXR和AR.js的AR体验
- **AR解密探宝游戏** - 互动式寻宝游戏，增加游览趣味性

### 🎨 界面特点
- 响应式设计，支持移动端和桌面端
- 现代化UI设计，操作简单直观
- 多模式切换，满足不同用户需求
- 实时进度跟踪和成就系统

### 🔧 技术特性
- 基于现代Web标准，无需安装应用
- 支持离线缓存，网络环境要求低
- 跨平台兼容，支持主流浏览器
- 模块化架构，易于维护和扩展

## 🚀 快速开始

### 环境要求
- Node.js 16.0 或更高版本
- 现代浏览器（支持WebGL和WebXR）
- HTTPS环境（AR功能需要）

### 安装步骤

1. **克隆项目**
```bash
git clone [repository-url]
cd tangshan-mine-park-ar-guide
```

2. **安装依赖**
```bash
npm install
```

3. **启动开发服务器**
```bash
npm run dev
```

4. **构建生产版本**
```bash
npm run build
```

5. **预览生产版本**
```bash
npm run preview
```

### 访问应用
- 开发环境: `https://localhost:3000`
- 生产环境: 部署到支持HTTPS的服务器

## 📱 使用指南

### 基本操作

#### 🖱️ 鼠标操作（桌面端）
- **左键拖拽**: 旋转3D视角
- **右键拖拽**: 平移视角
- **滚轮**: 缩放视角
- **双击**: 聚焦到景点

#### 📱 触摸操作（移动端）
- **单指拖拽**: 旋转视角
- **双指缩放**: 调整视距
- **双指拖拽**: 平移视角

#### ⌨️ 键盘快捷键
- **1-4**: 切换功能模式
- **R**: 重置相机视角
- **Ctrl+H**: 显示帮助
- **ESC**: 关闭面板

### 功能模块

#### 🏞️ 3D展示模式
- 浏览汤山矿坑公园的三维场景
- 查看景点详细信息
- 调整视角和场景设置

#### 🗺️ 路线规划模式
- 选择预设游览路线
- 自定义个性化路线
- 查看路线详情和导航

#### 📱 AR导览模式
- 启用设备相机进行AR体验
- 扫描景点获取增强信息
- 实时导航和信息叠加

#### 🎮 AR探宝模式
- 参与互动寻宝游戏
- 解密谜题获得积分
- 收集成就和徽章

## 🏗️ 项目结构

```
tangshan-mine-park-ar-guide/
├── index.html              # 主页面
├── package.json            # 项目配置
├── vite.config.js          # 构建配置
├── src/                    # 源代码目录
│   ├── main.js            # 应用入口
│   ├── styles/            # 样式文件
│   │   └── main.css       # 主样式
│   └── modules/           # 功能模块
│       ├── LoadingManager.js    # 加载管理
│       ├── DataManager.js       # 数据管理
│       ├── UIManager.js         # 界面管理
│       ├── SceneManager.js      # 3D场景管理
│       ├── ARManager.js         # AR功能管理
│       ├── RouteManager.js      # 路线管理
│       └── TreasureManager.js   # 探宝游戏管理
├── assets/                # 静态资源
│   ├── images/           # 图片资源
│   ├── models/           # 3D模型
│   ├── textures/         # 纹理贴图
│   ├── audio/            # 音频文件
│   └── markers/          # AR标记
└── dist/                 # 构建输出目录
```

## 🔧 技术栈

### 前端框架
- **Three.js** - 3D图形渲染
- **AR.js** - AR功能实现
- **A-Frame** - WebXR框架
- **Vite** - 构建工具

### 核心库
- **GSAP** - 动画库
- **dat.GUI** - 调试界面
- **Stats.js** - 性能监控

### 开发工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **Terser** - 代码压缩

## 🎯 景点信息

### 主要景点
1. **矿坑湖** - 核心景观，由废弃矿坑形成
2. **观景平台** - 最佳摄影点，可俯瞰全景
3. **生态步道** - 环湖步道，观赏植物和鸟类
4. **地质博物馆** - 了解地质历史和矿物
5. **儿童游乐区** - 亲子互动区域

### 游览路线
- **推荐路线** (2小时) - 涵盖主要景点
- **快速游览** (1小时) - 精华景点体验
- **深度游览** (3小时) - 完整游览体验
- **自定义路线** - 个性化路线规划

## 🎮 探宝游戏

### 宝藏类型
- **历史文物** - 矿工工具等历史遗迹
- **地质标本** - 稀有矿物和晶体
- **古代化石** - 远古时期的生物化石

### 游戏机制
- **位置探测** - 基于GPS的宝藏定位
- **解密挑战** - 谜题解答获得额外积分
- **成就系统** - 收集徽章和奖励
- **排行榜** - 与其他玩家比较成绩

## 🔒 隐私和安全

### 数据保护
- 位置信息仅用于AR功能，不会上传
- 用户进度保存在本地存储
- 不收集个人敏感信息

### 权限要求
- **相机权限** - AR功能必需
- **位置权限** - 探宝游戏可选
- **设备方向** - 增强AR体验

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循现有的代码风格
- 添加必要的注释和文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [Issues]
- 技术支持: [Email]

## 🙏 致谢

感谢以下开源项目的支持：
- Three.js 团队
- AR.js 社区
- A-Frame 开发者
- 所有贡献者

---

**汤山矿坑公园三维导览系统** - 让科技为旅游插上翅膀 🚀
