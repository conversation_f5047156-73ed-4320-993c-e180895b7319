<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="汤山矿坑公园三维导览系统">
    <title>汤山矿坑公园 - 三维AR导览系统</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="stylesheet" href="/src/styles/main.css">
</head>
<body>
    <div id="app">
        <!-- 加载界面 -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-content">
                <div class="loading-logo">
                    <h1>汤山矿坑公园</h1>
                    <p>三维AR导览系统</p>
                </div>
                <div class="loading-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <p id="loading-text">正在加载资源...</p>
                </div>
            </div>
        </div>

        <!-- 主导航 -->
        <nav id="main-nav" class="main-nav hidden">
            <div class="nav-brand">
                <h2>汤山矿坑公园</h2>
            </div>
            <div class="nav-menu">
                <button class="nav-btn active" data-mode="3d">3D展示</button>
                <button class="nav-btn" data-mode="route">路线规划</button>
                <button class="nav-btn" data-mode="ar">AR导览</button>
                <button class="nav-btn" data-mode="treasure">AR探宝</button>
            </div>
            <div class="nav-controls">
                <button id="settings-btn" class="control-btn">⚙️</button>
                <button id="help-btn" class="control-btn">❓</button>
            </div>
        </nav>

        <!-- 3D场景容器 -->
        <div id="scene-container" class="scene-container hidden">
            <canvas id="three-canvas"></canvas>
            
            <!-- 3D控制面板 -->
            <div id="scene-controls" class="scene-controls">
                <div class="control-group">
                    <label>视角控制</label>
                    <button id="reset-camera">重置视角</button>
                    <button id="aerial-view">鸟瞰视角</button>
                    <button id="ground-view">地面视角</button>
                </div>
                <div class="control-group">
                    <label>场景设置</label>
                    <button id="toggle-wireframe">线框模式</button>
                    <button id="toggle-lighting">光照效果</button>
                </div>
            </div>
        </div>

        <!-- AR场景容器 -->
        <div id="ar-container" class="ar-container hidden">
            <a-scene
                id="ar-scene"
                embedded
                arjs="sourceType: webcam; debugUIEnabled: false; detectionMode: mono_and_matrix; matrixCodeType: 3x3;"
                vr-mode-ui="enabled: false"
                renderer="logarithmicDepthBuffer: true;"
                loading-screen="enabled: false">
                
                <!-- AR相机 -->
                <a-camera
                    id="ar-camera"
                    gps-camera
                    rotation-reader>
                </a-camera>
                
                <!-- AR内容将动态添加 -->
            </a-scene>
            
            <!-- AR控制界面 -->
            <div id="ar-controls" class="ar-controls">
                <button id="ar-info-btn" class="ar-btn">📍 景点信息</button>
                <button id="ar-route-btn" class="ar-btn">🗺️ 路线导航</button>
                <button id="ar-camera-btn" class="ar-btn">📷 拍照</button>
            </div>
        </div>

        <!-- 路线规划界面 -->
        <div id="route-container" class="route-container hidden">
            <div class="route-sidebar">
                <h3>路线规划</h3>
                <div class="route-options">
                    <div class="route-type">
                        <label>
                            <input type="radio" name="route-type" value="recommended" checked>
                            推荐路线 (2小时)
                        </label>
                        <label>
                            <input type="radio" name="route-type" value="quick">
                            快速游览 (1小时)
                        </label>
                        <label>
                            <input type="radio" name="route-type" value="detailed">
                            深度游览 (3小时)
                        </label>
                        <label>
                            <input type="radio" name="route-type" value="custom">
                            自定义路线
                        </label>
                    </div>
                    <div class="poi-list">
                        <h4>景点列表</h4>
                        <div id="poi-checkboxes"></div>
                    </div>
                    <button id="generate-route" class="primary-btn">生成路线</button>
                </div>
            </div>
            <div class="route-map">
                <div id="route-3d-view"></div>
            </div>
        </div>

        <!-- 探宝游戏界面 -->
        <div id="treasure-container" class="treasure-container hidden">
            <div class="treasure-hud">
                <div class="treasure-score">
                    <span>积分: <span id="treasure-points">0</span></span>
                    <span>宝藏: <span id="treasures-found">0</span>/10</span>
                </div>
                <div class="treasure-hint">
                    <p id="treasure-hint-text">使用AR扫描周围环境寻找隐藏的宝藏！</p>
                </div>
            </div>
            <div id="treasure-ar-view"></div>
        </div>

        <!-- 信息面板 -->
        <div id="info-panel" class="info-panel hidden">
            <div class="info-content">
                <button class="close-btn" id="close-info">×</button>
                <div id="info-body"></div>
            </div>
        </div>

        <!-- 设置面板 -->
        <div id="settings-panel" class="settings-panel hidden">
            <div class="settings-content">
                <h3>设置</h3>
                <div class="setting-group">
                    <label>图形质量</label>
                    <select id="quality-select">
                        <option value="low">低</option>
                        <option value="medium" selected>中</option>
                        <option value="high">高</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>音效</label>
                    <input type="range" id="volume-slider" min="0" max="100" value="50">
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="auto-rotate" checked>
                        自动旋转
                    </label>
                </div>
                <button id="close-settings" class="primary-btn">确定</button>
            </div>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>
</html>
