// 三维场景管理器 - 使用Three.js管理3D场景
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { Water } from 'three/examples/jsm/objects/Water.js';
import { Sky } from 'three/examples/jsm/objects/Sky.js';
import Stats from 'stats.js';
import { ModelManager } from './ModelManager.js';
import { MODEL_CONFIGS, PERFORMANCE_CONFIGS, getHighPriorityModels, getMediumPriorityModels, getLowPriorityModels } from '../config/models.js';

export class SceneManager {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.stats = null;

        // 模型管理器
        this.modelManager = null;
        this.loadedModels = new Map();
        this.poiMarkers = new Map();
        this.water = null;
        this.sky = null;

        // 加载状态
        this.isInitialized = false;
        this.isPaused = false;
        this.animationId = null;
        this.loadingProgress = 0;
        this.totalModelsToLoad = 0;
        this.loadedModelsCount = 0;

        // 场景设置
        this.settings = {
            enableShadows: true,
            enableWireframe: false,
            enableStats: false,
            quality: 'medium',
            performanceLevel: 'medium' // high, medium, low
        };

        // 性能监控
        this.performanceMonitor = {
            frameCount: 0,
            lastTime: 0,
            fps: 0
        };
    }

    async init() {
        try {
            this.initRenderer();
            this.initScene();
            this.initCamera();
            this.initControls();
            this.initLighting();
            this.initStats();

            // 初始化模型管理器
            this.modelManager = new ModelManager(this.scene, this.renderer);
            this.setupModelManagerCallbacks();

            // 设置性能配置
            const performanceConfig = PERFORMANCE_CONFIGS[this.settings.performanceLevel];
            this.modelManager.setPerformanceConfig(performanceConfig);

            await this.loadEnvironment();
            await this.loadAllModels();
            this.createPOIMarkers();

            this.bindEvents();
            this.startRenderLoop();

            this.isInitialized = true;
            console.log('三维场景管理器初始化完成');
        } catch (error) {
            console.error('三维场景管理器初始化失败:', error);
            throw error;
        }
    }

    initRenderer() {
        const canvas = document.getElementById('three-canvas');
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight - 70);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = this.settings.enableShadows;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 0.8;
    }

    initScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0xcccccc, 100, 2000);
    }

    initCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            (window.innerWidth) / (window.innerHeight - 70),
            0.1,
            2000
        );
        this.camera.position.set(100, 50, 100);
        this.camera.lookAt(0, 0, 0);
    }

    initControls() {
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.minDistance = 10;
        this.controls.maxDistance = 500;
        this.controls.maxPolarAngle = Math.PI / 2.1;
        this.controls.autoRotate = true;
        this.controls.autoRotateSpeed = 0.5;
    }

    initLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // 主光源（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);

        // 补充光源
        const fillLight = new THREE.DirectionalLight(0x87CEEB, 0.3);
        fillLight.position.set(-50, 30, -50);
        this.scene.add(fillLight);
    }

    initStats() {
        if (this.settings.enableStats) {
            this.stats = new Stats();
            this.stats.showPanel(0);
            document.body.appendChild(this.stats.dom);
            this.stats.dom.style.position = 'fixed';
            this.stats.dom.style.top = '80px';
            this.stats.dom.style.left = '10px';
            this.stats.dom.style.zIndex = '1000';
        }
    }

    async loadEnvironment() {
        // 创建地形
        await this.createTerrain();
        
        // 创建水面
        await this.createWater();
        
        // 创建天空
        this.createSky();
        
        // 创建植被
        this.createVegetation();
    }

    async createTerrain() {
        // 创建矿坑地形
        const terrainGeometry = new THREE.PlaneGeometry(200, 200, 50, 50);
        
        // 修改顶点创建矿坑形状
        const vertices = terrainGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const x = vertices[i];
            const z = vertices[i + 2];
            const distance = Math.sqrt(x * x + z * z);
            
            // 创建矿坑形状
            if (distance < 40) {
                vertices[i + 1] = -Math.pow((40 - distance) / 40, 2) * 20; // Y坐标
            } else {
                vertices[i + 1] = Math.random() * 2; // 地面起伏
            }
        }
        
        terrainGeometry.attributes.position.needsUpdate = true;
        terrainGeometry.computeVertexNormals();
        
        const terrainMaterial = new THREE.MeshLambertMaterial({
            color: 0x8B7355,
            wireframe: this.settings.enableWireframe
        });
        
        const terrain = new THREE.Mesh(terrainGeometry, terrainMaterial);
        terrain.rotation.x = -Math.PI / 2;
        terrain.receiveShadow = true;
        this.scene.add(terrain);
        
        this.models.set('terrain', terrain);
    }

    async createWater() {
        const waterGeometry = new THREE.CircleGeometry(35, 32);
        
        this.water = new Water(waterGeometry, {
            textureWidth: 512,
            textureHeight: 512,
            waterNormals: new THREE.TextureLoader().load('/assets/textures/waternormals.jpg', (texture) => {
                texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
            }),
            sunDirection: new THREE.Vector3(),
            sunColor: 0xffffff,
            waterColor: 0x001e0f,
            distortionScale: 3.7,
            fog: this.scene.fog !== undefined
        });
        
        this.water.rotation.x = -Math.PI / 2;
        this.water.position.y = -15;
        this.scene.add(this.water);
    }

    createSky() {
        this.sky = new Sky();
        this.sky.scale.setScalar(450000);
        this.scene.add(this.sky);

        const skyUniforms = this.sky.material.uniforms;
        skyUniforms['turbidity'].value = 10;
        skyUniforms['rayleigh'].value = 2;
        skyUniforms['mieCoefficient'].value = 0.005;
        skyUniforms['mieDirectionalG'].value = 0.8;

        const sun = new THREE.Vector3();
        const phi = THREE.MathUtils.degToRad(90 - 30); // 太阳高度角
        const theta = THREE.MathUtils.degToRad(180); // 太阳方位角
        
        sun.setFromSphericalCoords(1, phi, theta);
        skyUniforms['sunPosition'].value.copy(sun);
        
        if (this.water) {
            this.water.material.uniforms['sunDirection'].value.copy(sun).normalize();
        }
    }

    createVegetation() {
        // 创建简单的树木
        const treeGroup = new THREE.Group();
        
        for (let i = 0; i < 20; i++) {
            const tree = this.createTree();
            const angle = (i / 20) * Math.PI * 2;
            const radius = 60 + Math.random() * 40;
            
            tree.position.x = Math.cos(angle) * radius;
            tree.position.z = Math.sin(angle) * radius;
            tree.position.y = Math.random() * 2;
            
            treeGroup.add(tree);
        }
        
        this.scene.add(treeGroup);
        this.models.set('vegetation', treeGroup);
    }

    createTree() {
        const tree = new THREE.Group();
        
        // 树干
        const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 4;
        trunk.castShadow = true;
        tree.add(trunk);
        
        // 树冠
        const crownGeometry = new THREE.SphereGeometry(4, 8, 6);
        const crownMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const crown = new THREE.Mesh(crownGeometry, crownMaterial);
        crown.position.y = 10;
        crown.castShadow = true;
        tree.add(crown);
        
        return tree;
    }

    // 设置模型管理器回调
    setupModelManagerCallbacks() {
        this.modelManager.setCallbacks(
            (progress, url) => {
                this.onModelLoadProgress(progress, url);
            },
            (error, url) => {
                this.onModelLoadError(error, url);
            }
        );
    }

    // 模型加载进度回调
    onModelLoadProgress(progress, url) {
        if (progress.lengthComputable) {
            const percentComplete = (progress.loaded / progress.total) * 100;
            console.log(`模型加载进度: ${Math.round(percentComplete)}% - ${url}`);

            // 更新全局加载进度
            this.updateLoadingProgress();
        }
    }

    // 模型加载错误回调
    onModelLoadError(error, url) {
        console.error(`模型加载失败: ${url}`, error);

        // 尝试加载备用模型或创建简单几何体
        this.createFallbackModel(url);
    }

    // 更新加载进度
    updateLoadingProgress() {
        this.loadedModelsCount++;
        this.loadingProgress = (this.loadedModelsCount / this.totalModelsToLoad) * 100;

        // 触发进度更新事件
        window.dispatchEvent(new CustomEvent('modelLoadingProgress', {
            detail: {
                progress: this.loadingProgress,
                loaded: this.loadedModelsCount,
                total: this.totalModelsToLoad
            }
        }));
    }

    // 分阶段加载所有模型
    async loadAllModels() {
        try {
            console.log('开始加载3D模型...');

            // 第一阶段：加载高优先级模型（核心建筑和地形）
            await this.loadHighPriorityModels();

            // 第二阶段：加载中优先级模型（重要装饰）
            await this.loadMediumPriorityModels();

            // 第三阶段：加载低优先级模型（细节装饰）
            await this.loadLowPriorityModels();

            console.log('所有3D模型加载完成');
        } catch (error) {
            console.error('模型加载过程中出现错误:', error);
            // 继续使用程序生成的几何体作为备用
            this.createFallbackModels();
        }
    }

    // 加载高优先级模型
    async loadHighPriorityModels() {
        const highPriorityModels = getHighPriorityModels();
        this.totalModelsToLoad += highPriorityModels.length;

        console.log(`加载${highPriorityModels.length}个高优先级模型...`);

        for (const modelConfig of highPriorityModels) {
            try {
                const model = await this.loadSingleModel(modelConfig);
                if (model) {
                    this.addModelToScene(model, modelConfig);
                }
            } catch (error) {
                console.warn(`高优先级模型加载失败: ${modelConfig.id}`, error);
                this.createFallbackModel(modelConfig);
            }
        }
    }

    // 加载中优先级模型
    async loadMediumPriorityModels() {
        const mediumPriorityModels = getMediumPriorityModels();
        this.totalModelsToLoad += mediumPriorityModels.length;

        console.log(`加载${mediumPriorityModels.length}个中优先级模型...`);

        // 延迟加载，避免阻塞主要内容
        setTimeout(async () => {
            for (const modelConfig of mediumPriorityModels) {
                try {
                    const model = await this.loadSingleModel(modelConfig);
                    if (model) {
                        this.addModelToScene(model, modelConfig);
                    }
                } catch (error) {
                    console.warn(`中优先级模型加载失败: ${modelConfig.id}`, error);
                }
            }
        }, 1000);
    }

    // 加载低优先级模型
    async loadLowPriorityModels() {
        const lowPriorityModels = getLowPriorityModels();
        this.totalModelsToLoad += lowPriorityModels.length;

        console.log(`加载${lowPriorityModels.length}个低优先级模型...`);

        // 更长延迟加载，确保不影响核心体验
        setTimeout(async () => {
            for (const modelConfig of lowPriorityModels) {
                try {
                    const model = await this.loadSingleModel(modelConfig);
                    if (model) {
                        this.addModelToScene(model, modelConfig);
                    }
                } catch (error) {
                    console.warn(`低优先级模型加载失败: ${modelConfig.id}`, error);
                }
            }
        }, 3000);
    }

    // 加载单个模型
    async loadSingleModel(modelConfig) {
        switch (modelConfig.type) {
            case 'gltf':
            case 'glb':
                return await this.modelManager.loadGLTFModel(modelConfig.url, modelConfig.options);
            case 'fbx':
                return await this.modelManager.loadFBXModel(modelConfig.url, modelConfig.options);
            case 'obj':
                return await this.modelManager.loadOBJModel(modelConfig.url, modelConfig.mtlUrl, modelConfig.options);
            default:
                throw new Error(`不支持的模型格式: ${modelConfig.type}`);
        }
    }

    // 将模型添加到场景
    addModelToScene(model, modelConfig) {
        // 处理实例化模型
        if (modelConfig.instances && modelConfig.instances.length > 0) {
            modelConfig.instances.forEach((instanceConfig, index) => {
                const instance = model.clone();

                if (instanceConfig.position) {
                    instance.position.set(
                        instanceConfig.position.x,
                        instanceConfig.position.y,
                        instanceConfig.position.z
                    );
                }

                if (instanceConfig.rotation) {
                    instance.rotation.set(
                        instanceConfig.rotation.x || 0,
                        instanceConfig.rotation.y || 0,
                        instanceConfig.rotation.z || 0
                    );
                }

                if (instanceConfig.scale) {
                    instance.scale.setScalar(instanceConfig.scale);
                }

                instance.userData = {
                    modelId: `${modelConfig.id}_${index}`,
                    category: modelConfig.category,
                    description: modelConfig.description
                };

                this.scene.add(instance);
                this.loadedModels.set(`${modelConfig.id}_${index}`, instance);
            });
        } else {
            // 单个模型
            model.userData = {
                modelId: modelConfig.id,
                category: modelConfig.category,
                description: modelConfig.description
            };

            this.scene.add(model);
            this.loadedModels.set(modelConfig.id, model);
        }

        console.log(`模型已添加到场景: ${modelConfig.id}`);
    }

    // 创建备用模型（当3D模型加载失败时使用）
    createFallbackModels() {
        console.log('创建备用几何体模型...');

        // 创建观景台
        const platformGeometry = new THREE.BoxGeometry(10, 2, 10);
        const platformMaterial = new THREE.MeshLambertMaterial({ color: 0xCCCCCC });
        const platform = new THREE.Mesh(platformGeometry, platformMaterial);
        platform.position.set(50, 21, 30);
        platform.castShadow = true;
        platform.receiveShadow = true;
        platform.userData = { modelId: 'fallback_viewpoint', category: 'buildings' };
        this.scene.add(platform);
        this.loadedModels.set('fallback_viewpoint', platform);

        // 创建博物馆建筑
        const museumGeometry = new THREE.BoxGeometry(20, 8, 15);
        const museumMaterial = new THREE.MeshLambertMaterial({ color: 0xDDDDDD });
        const museum = new THREE.Mesh(museumGeometry, museumMaterial);
        museum.position.set(80, 14, -20);
        museum.castShadow = true;
        museum.receiveShadow = true;
        museum.userData = { modelId: 'fallback_museum', category: 'buildings' };
        this.scene.add(museum);
        this.loadedModels.set('fallback_museum', museum);

        // 创建游客中心
        const visitorCenterGeometry = new THREE.BoxGeometry(15, 6, 12);
        const visitorCenterMaterial = new THREE.MeshLambertMaterial({ color: 0xE8E8E8 });
        const visitorCenter = new THREE.Mesh(visitorCenterGeometry, visitorCenterMaterial);
        visitorCenter.position.set(-80, 8, -60);
        visitorCenter.castShadow = true;
        visitorCenter.receiveShadow = true;
        visitorCenter.userData = { modelId: 'fallback_visitor_center', category: 'buildings' };
        this.scene.add(visitorCenter);
        this.loadedModels.set('fallback_visitor_center', visitorCenter);
    }

    // 为特定模型创建备用几何体
    createFallbackModel(modelConfig) {
        console.log(`为${modelConfig.id}创建备用模型`);

        let geometry, material, mesh;

        // 根据模型类别创建不同的备用几何体
        switch (modelConfig.category) {
            case 'buildings':
                geometry = new THREE.BoxGeometry(5, 3, 5);
                material = new THREE.MeshLambertMaterial({ color: 0xCCCCCC });
                break;
            case 'vegetation':
                geometry = new THREE.ConeGeometry(2, 8, 8);
                material = new THREE.MeshLambertMaterial({ color: 0x228B22 });
                break;
            case 'details':
                geometry = new THREE.CylinderGeometry(0.5, 0.5, 2);
                material = new THREE.MeshLambertMaterial({ color: 0x666666 });
                break;
            default:
                geometry = new THREE.BoxGeometry(2, 2, 2);
                material = new THREE.MeshLambertMaterial({ color: 0x888888 });
        }

        mesh = new THREE.Mesh(geometry, material);

        // 应用配置
        if (modelConfig.options) {
            if (modelConfig.options.position) {
                mesh.position.copy(modelConfig.options.position);
            }
            if (modelConfig.options.rotation) {
                mesh.rotation.copy(modelConfig.options.rotation);
            }
            if (modelConfig.options.scale) {
                mesh.scale.setScalar(modelConfig.options.scale);
            }
            if (modelConfig.options.castShadow) {
                mesh.castShadow = true;
            }
            if (modelConfig.options.receiveShadow) {
                mesh.receiveShadow = true;
            }
        }

        mesh.userData = {
            modelId: `fallback_${modelConfig.id}`,
            category: modelConfig.category,
            description: `备用模型: ${modelConfig.description}`,
            isFallback: true
        };

        this.scene.add(mesh);
        this.loadedModels.set(`fallback_${modelConfig.id}`, mesh);
    }

    // 获取模型信息
    getModelInfo(modelId) {
        const model = this.loadedModels.get(modelId);
        if (!model) return null;

        if (this.modelManager) {
            return this.modelManager.getModelInfo(model);
        }

        // 备用信息获取
        let triangleCount = 0;
        let vertexCount = 0;

        model.traverse((child) => {
            if (child.isMesh && child.geometry) {
                const positions = child.geometry.attributes.position;
                if (positions) {
                    vertexCount += positions.count;
                    triangleCount += positions.count / 3;
                }
            }
        });

        return {
            triangles: Math.floor(triangleCount),
            vertices: vertexCount,
            materials: 1,
            isFallback: model.userData.isFallback || false
        };
    }

    // 切换模型显示/隐藏
    toggleModelVisibility(modelId, visible = null) {
        const model = this.loadedModels.get(modelId);
        if (model) {
            model.visible = visible !== null ? visible : !model.visible;
            console.log(`模型 ${modelId} 可见性: ${model.visible}`);
        }
    }

    // 按类别切换模型显示
    toggleCategoryVisibility(category, visible = null) {
        let count = 0;
        this.loadedModels.forEach((model, id) => {
            if (model.userData.category && model.userData.category.includes(category)) {
                model.visible = visible !== null ? visible : !model.visible;
                count++;
            }
        });
        console.log(`切换了 ${count} 个 ${category} 类别的模型可见性`);
    }

    // 获取场景统计信息
    getSceneStats() {
        let totalTriangles = 0;
        let totalVertices = 0;
        let totalModels = 0;
        let fallbackModels = 0;

        this.loadedModels.forEach((model) => {
            totalModels++;
            if (model.userData.isFallback) {
                fallbackModels++;
            }

            model.traverse((child) => {
                if (child.isMesh && child.geometry) {
                    const positions = child.geometry.attributes.position;
                    if (positions) {
                        totalVertices += positions.count;
                        totalTriangles += positions.count / 3;
                    }
                }
            });
        });

        return {
            totalModels,
            fallbackModels,
            loadedModels: totalModels - fallbackModels,
            totalTriangles: Math.floor(totalTriangles),
            totalVertices,
            loadingProgress: this.loadingProgress,
            fps: this.performanceMonitor.fps
        };
    }

    createPOIMarkers() {
        // 创建景点标记
        const markerGeometry = new THREE.SphereGeometry(2, 16, 16);
        const markerMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xff4444,
            transparent: true,
            opacity: 0.8
        });
        
        // 示例标记位置
        const poiPositions = [
            { id: 'poi_001', position: new THREE.Vector3(0, -10, 0) },
            { id: 'poi_002', position: new THREE.Vector3(50, 25, 30) },
            { id: 'poi_003', position: new THREE.Vector3(-30, 8, 20) },
            { id: 'poi_004', position: new THREE.Vector3(80, 18, -20) },
            { id: 'poi_005', position: new THREE.Vector3(-60, 3, -40) }
        ];
        
        poiPositions.forEach(poi => {
            const marker = new THREE.Mesh(markerGeometry, markerMaterial.clone());
            marker.position.copy(poi.position);
            marker.userData = { poiId: poi.id };
            
            // 添加标记动画
            marker.userData.originalY = marker.position.y;
            
            this.scene.add(marker);
            this.poiMarkers.set(poi.id, marker);
        });
    }

    bindEvents() {
        // 场景控制按钮事件
        document.getElementById('reset-camera').addEventListener('click', () => {
            this.resetCamera();
        });
        
        document.getElementById('aerial-view').addEventListener('click', () => {
            this.setAerialView();
        });
        
        document.getElementById('ground-view').addEventListener('click', () => {
            this.setGroundView();
        });
        
        document.getElementById('toggle-wireframe').addEventListener('click', () => {
            this.toggleWireframe();
        });
        
        document.getElementById('toggle-lighting').addEventListener('click', () => {
            this.toggleLighting();
        });

        // 鼠标点击事件
        this.renderer.domElement.addEventListener('click', (event) => {
            this.handleClick(event);
        });

        // 设置变更事件
        window.addEventListener('graphicsQualityChanged', (e) => {
            this.updateGraphicsQuality(e.detail.quality);
        });

        window.addEventListener('autoRotateChanged', (e) => {
            this.controls.autoRotate = e.detail.enabled;
        });
    }

    startRenderLoop() {
        const animate = () => {
            if (!this.isPaused) {
                this.animationId = requestAnimationFrame(animate);
                this.update();
                this.render();
            }
        };
        animate();
    }

    update() {
        if (this.stats) this.stats.begin();

        // 性能监控
        this.updatePerformanceMonitor();

        this.controls.update();

        // 更新水面
        if (this.water) {
            this.water.material.uniforms['time'].value += 1.0 / 60.0;
        }

        // 更新POI标记动画
        this.poiMarkers.forEach(marker => {
            marker.position.y = marker.userData.originalY + Math.sin(Date.now() * 0.002) * 2;
        });

        // 更新模型动画
        this.updateModelAnimations();

        if (this.stats) this.stats.end();
    }

    // 更新性能监控
    updatePerformanceMonitor() {
        const currentTime = performance.now();
        this.performanceMonitor.frameCount++;

        if (currentTime - this.performanceMonitor.lastTime >= 1000) {
            this.performanceMonitor.fps = this.performanceMonitor.frameCount;
            this.performanceMonitor.frameCount = 0;
            this.performanceMonitor.lastTime = currentTime;

            // 根据FPS自动调整性能设置
            this.autoAdjustPerformance();
        }
    }

    // 更新模型动画
    updateModelAnimations() {
        const deltaTime = 0.016; // 假设60fps

        this.loadedModels.forEach((model) => {
            if (model.userData.mixer) {
                model.userData.mixer.update(deltaTime);
            }
        });
    }

    // 自动调整性能设置
    autoAdjustPerformance() {
        const fps = this.performanceMonitor.fps;

        if (fps < 30 && this.settings.performanceLevel !== 'low') {
            console.log('FPS过低，自动降低性能设置');
            this.setPerformanceLevel('low');
        } else if (fps > 50 && this.settings.performanceLevel === 'low') {
            console.log('FPS良好，提升性能设置');
            this.setPerformanceLevel('medium');
        }
    }

    // 设置性能级别
    setPerformanceLevel(level) {
        this.settings.performanceLevel = level;
        const config = PERFORMANCE_CONFIGS[level];

        if (this.modelManager) {
            this.modelManager.setPerformanceConfig(config);
        }

        // 更新渲染器设置
        this.renderer.shadowMap.enabled = config.shadowMapSize > 0;
        if (config.shadowMapSize > 0) {
            this.renderer.shadowMap.mapSize.width = config.shadowMapSize;
            this.renderer.shadowMap.mapSize.height = config.shadowMapSize;
        }

        // 更新材质设置
        this.updateMaterialsForPerformance(config);

        console.log(`性能级别已设置为: ${level}`);
    }

    // 根据性能配置更新材质
    updateMaterialsForPerformance(config) {
        this.loadedModels.forEach((model) => {
            model.traverse((child) => {
                if (child.isMesh && child.material) {
                    const materials = Array.isArray(child.material) ? child.material : [child.material];

                    materials.forEach((material) => {
                        // 根据性能级别调整材质设置
                        if (material.map) {
                            material.map.anisotropy = config.anisotropy;
                        }

                        // 低性能设备禁用一些高级特性
                        if (config.maxTriangles < 300000) {
                            if (material.normalMap) {
                                material.normalMap = null;
                            }
                            if (material.roughnessMap) {
                                material.roughnessMap = null;
                            }
                        }
                    });
                }
            });
        });
    }

    render() {
        this.renderer.render(this.scene, this.camera);
    }

    // 公共方法
    resetCamera() {
        this.camera.position.set(100, 50, 100);
        this.camera.lookAt(0, 0, 0);
        this.controls.reset();
    }

    setAerialView() {
        this.camera.position.set(0, 150, 0);
        this.camera.lookAt(0, 0, 0);
        this.controls.update();
    }

    setGroundView() {
        this.camera.position.set(80, 10, 80);
        this.camera.lookAt(0, 0, 0);
        this.controls.update();
    }

    toggleWireframe() {
        this.settings.enableWireframe = !this.settings.enableWireframe;
        this.models.forEach(model => {
            if (model.material) {
                model.material.wireframe = this.settings.enableWireframe;
            }
        });
    }

    toggleLighting() {
        // 切换光照效果的实现
        this.scene.children.forEach(child => {
            if (child.isLight && child.type !== 'AmbientLight') {
                child.visible = !child.visible;
            }
        });
    }

    handleClick(event) {
        const mouse = new THREE.Vector2();
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / (window.innerHeight - 70)) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);

        const intersects = raycaster.intersectObjects(Array.from(this.poiMarkers.values()));
        
        if (intersects.length > 0) {
            const clickedMarker = intersects[0].object;
            const poiId = clickedMarker.userData.poiId;
            this.onPOIClicked(poiId);
        }
    }

    onPOIClicked(poiId) {
        // 触发POI点击事件
        window.dispatchEvent(new CustomEvent('poiClicked', { 
            detail: { poiId } 
        }));
    }

    updateGraphicsQuality(quality) {
        this.settings.quality = quality;
        
        switch (quality) {
            case 'low':
                this.renderer.setPixelRatio(1);
                this.renderer.shadowMap.enabled = false;
                break;
            case 'medium':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
                this.renderer.shadowMap.enabled = true;
                break;
            case 'high':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                this.renderer.shadowMap.enabled = true;
                break;
        }
    }

    handleResize() {
        if (!this.isInitialized) return;
        
        this.camera.aspect = window.innerWidth / (window.innerHeight - 70);
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight - 70);
    }

    pause() {
        this.isPaused = true;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    resume() {
        this.isPaused = false;
        this.startRenderLoop();
    }

    dispose() {
        this.pause();

        // 清理模型资源
        this.loadedModels.forEach(model => {
            this.disposeModel(model);
        });
        this.loadedModels.clear();

        // 清理模型管理器
        if (this.modelManager) {
            this.modelManager.dispose();
        }

        // 清理水面和天空
        if (this.water) {
            this.water.geometry.dispose();
            this.water.material.dispose();
        }

        if (this.sky) {
            this.sky.geometry.dispose();
            this.sky.material.dispose();
        }

        // 清理渲染器
        if (this.renderer) {
            this.renderer.dispose();
        }

        // 清理统计面板
        if (this.stats && this.stats.dom.parentNode) {
            this.stats.dom.parentNode.removeChild(this.stats.dom);
        }

        console.log('场景管理器资源已清理');
    }

    // 清理单个模型
    disposeModel(model) {
        model.traverse((child) => {
            if (child.isMesh) {
                if (child.geometry) {
                    child.geometry.dispose();
                }

                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => this.disposeMaterial(material));
                    } else {
                        this.disposeMaterial(child.material);
                    }
                }
            }
        });

        // 清理动画混合器
        if (model.userData.mixer) {
            model.userData.mixer.stopAllAction();
            model.userData.mixer = null;
        }
    }

    // 清理材质
    disposeMaterial(material) {
        if (material.map) material.map.dispose();
        if (material.normalMap) material.normalMap.dispose();
        if (material.roughnessMap) material.roughnessMap.dispose();
        if (material.metalnessMap) material.metalnessMap.dispose();
        if (material.aoMap) material.aoMap.dispose();
        if (material.emissiveMap) material.emissiveMap.dispose();
        if (material.envMap) material.envMap.dispose();
        material.dispose();
    }

    // 获取加载的模型列表
    getLoadedModels() {
        const models = [];
        this.loadedModels.forEach((model, id) => {
            models.push({
                id,
                category: model.userData.category,
                description: model.userData.description,
                visible: model.visible,
                isFallback: model.userData.isFallback || false,
                info: this.getModelInfo(id)
            });
        });
        return models;
    }

    // 重新加载模型
    async reloadModel(modelId) {
        const model = this.loadedModels.get(modelId);
        if (!model) {
            console.warn(`模型不存在: ${modelId}`);
            return;
        }

        // 移除当前模型
        this.scene.remove(model);
        this.disposeModel(model);
        this.loadedModels.delete(modelId);

        // 尝试重新加载
        try {
            // 这里需要保存原始配置信息来重新加载
            console.log(`重新加载模型: ${modelId}`);
            // 实际实现需要保存模型配置
        } catch (error) {
            console.error(`重新加载模型失败: ${modelId}`, error);
            // 创建备用模型
            this.createFallbackModel({ id: modelId, category: model.userData.category });
        }
    }
}
