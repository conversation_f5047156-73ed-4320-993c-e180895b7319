// 三维场景管理器 - 使用Three.js管理3D场景
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { Water } from 'three/examples/jsm/objects/Water.js';
import { Sky } from 'three/examples/jsm/objects/Sky.js';
import Stats from 'stats.js';

export class SceneManager {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.stats = null;
        
        this.models = new Map();
        this.poiMarkers = new Map();
        this.water = null;
        this.sky = null;
        
        this.isInitialized = false;
        this.isPaused = false;
        this.animationId = null;
        
        // 场景设置
        this.settings = {
            enableShadows: true,
            enableWireframe: false,
            enableStats: false,
            quality: 'medium'
        };
    }

    async init() {
        try {
            this.initRenderer();
            this.initScene();
            this.initCamera();
            this.initControls();
            this.initLighting();
            this.initStats();
            
            await this.loadEnvironment();
            await this.loadModels();
            this.createPOIMarkers();
            
            this.bindEvents();
            this.startRenderLoop();
            
            this.isInitialized = true;
            console.log('三维场景管理器初始化完成');
        } catch (error) {
            console.error('三维场景管理器初始化失败:', error);
            throw error;
        }
    }

    initRenderer() {
        const canvas = document.getElementById('three-canvas');
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight - 70);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = this.settings.enableShadows;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 0.8;
    }

    initScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0xcccccc, 100, 2000);
    }

    initCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            (window.innerWidth) / (window.innerHeight - 70),
            0.1,
            2000
        );
        this.camera.position.set(100, 50, 100);
        this.camera.lookAt(0, 0, 0);
    }

    initControls() {
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.minDistance = 10;
        this.controls.maxDistance = 500;
        this.controls.maxPolarAngle = Math.PI / 2.1;
        this.controls.autoRotate = true;
        this.controls.autoRotateSpeed = 0.5;
    }

    initLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // 主光源（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);

        // 补充光源
        const fillLight = new THREE.DirectionalLight(0x87CEEB, 0.3);
        fillLight.position.set(-50, 30, -50);
        this.scene.add(fillLight);
    }

    initStats() {
        if (this.settings.enableStats) {
            this.stats = new Stats();
            this.stats.showPanel(0);
            document.body.appendChild(this.stats.dom);
            this.stats.dom.style.position = 'fixed';
            this.stats.dom.style.top = '80px';
            this.stats.dom.style.left = '10px';
            this.stats.dom.style.zIndex = '1000';
        }
    }

    async loadEnvironment() {
        // 创建地形
        await this.createTerrain();
        
        // 创建水面
        await this.createWater();
        
        // 创建天空
        this.createSky();
        
        // 创建植被
        this.createVegetation();
    }

    async createTerrain() {
        // 创建矿坑地形
        const terrainGeometry = new THREE.PlaneGeometry(200, 200, 50, 50);
        
        // 修改顶点创建矿坑形状
        const vertices = terrainGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const x = vertices[i];
            const z = vertices[i + 2];
            const distance = Math.sqrt(x * x + z * z);
            
            // 创建矿坑形状
            if (distance < 40) {
                vertices[i + 1] = -Math.pow((40 - distance) / 40, 2) * 20; // Y坐标
            } else {
                vertices[i + 1] = Math.random() * 2; // 地面起伏
            }
        }
        
        terrainGeometry.attributes.position.needsUpdate = true;
        terrainGeometry.computeVertexNormals();
        
        const terrainMaterial = new THREE.MeshLambertMaterial({
            color: 0x8B7355,
            wireframe: this.settings.enableWireframe
        });
        
        const terrain = new THREE.Mesh(terrainGeometry, terrainMaterial);
        terrain.rotation.x = -Math.PI / 2;
        terrain.receiveShadow = true;
        this.scene.add(terrain);
        
        this.models.set('terrain', terrain);
    }

    async createWater() {
        const waterGeometry = new THREE.CircleGeometry(35, 32);
        
        this.water = new Water(waterGeometry, {
            textureWidth: 512,
            textureHeight: 512,
            waterNormals: new THREE.TextureLoader().load('/assets/textures/waternormals.jpg', (texture) => {
                texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
            }),
            sunDirection: new THREE.Vector3(),
            sunColor: 0xffffff,
            waterColor: 0x001e0f,
            distortionScale: 3.7,
            fog: this.scene.fog !== undefined
        });
        
        this.water.rotation.x = -Math.PI / 2;
        this.water.position.y = -15;
        this.scene.add(this.water);
    }

    createSky() {
        this.sky = new Sky();
        this.sky.scale.setScalar(450000);
        this.scene.add(this.sky);

        const skyUniforms = this.sky.material.uniforms;
        skyUniforms['turbidity'].value = 10;
        skyUniforms['rayleigh'].value = 2;
        skyUniforms['mieCoefficient'].value = 0.005;
        skyUniforms['mieDirectionalG'].value = 0.8;

        const sun = new THREE.Vector3();
        const phi = THREE.MathUtils.degToRad(90 - 30); // 太阳高度角
        const theta = THREE.MathUtils.degToRad(180); // 太阳方位角
        
        sun.setFromSphericalCoords(1, phi, theta);
        skyUniforms['sunPosition'].value.copy(sun);
        
        if (this.water) {
            this.water.material.uniforms['sunDirection'].value.copy(sun).normalize();
        }
    }

    createVegetation() {
        // 创建简单的树木
        const treeGroup = new THREE.Group();
        
        for (let i = 0; i < 20; i++) {
            const tree = this.createTree();
            const angle = (i / 20) * Math.PI * 2;
            const radius = 60 + Math.random() * 40;
            
            tree.position.x = Math.cos(angle) * radius;
            tree.position.z = Math.sin(angle) * radius;
            tree.position.y = Math.random() * 2;
            
            treeGroup.add(tree);
        }
        
        this.scene.add(treeGroup);
        this.models.set('vegetation', treeGroup);
    }

    createTree() {
        const tree = new THREE.Group();
        
        // 树干
        const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 4;
        trunk.castShadow = true;
        tree.add(trunk);
        
        // 树冠
        const crownGeometry = new THREE.SphereGeometry(4, 8, 6);
        const crownMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const crown = new THREE.Mesh(crownGeometry, crownMaterial);
        crown.position.y = 10;
        crown.castShadow = true;
        tree.add(crown);
        
        return tree;
    }

    async loadModels() {
        const loader = new GLTFLoader();
        
        // 这里可以加载更复杂的3D模型
        // 由于演示目的，我们使用程序生成的几何体
        this.createBuildings();
    }

    createBuildings() {
        // 创建观景台
        const platformGeometry = new THREE.BoxGeometry(10, 2, 10);
        const platformMaterial = new THREE.MeshLambertMaterial({ color: 0xCCCCCC });
        const platform = new THREE.Mesh(platformGeometry, platformMaterial);
        platform.position.set(50, 21, 30);
        platform.castShadow = true;
        platform.receiveShadow = true;
        this.scene.add(platform);
        this.models.set('viewpoint', platform);
        
        // 创建博物馆建筑
        const museumGeometry = new THREE.BoxGeometry(20, 8, 15);
        const museumMaterial = new THREE.MeshLambertMaterial({ color: 0xDDDDDD });
        const museum = new THREE.Mesh(museumGeometry, museumMaterial);
        museum.position.set(80, 14, -20);
        museum.castShadow = true;
        museum.receiveShadow = true;
        this.scene.add(museum);
        this.models.set('museum', museum);
    }

    createPOIMarkers() {
        // 创建景点标记
        const markerGeometry = new THREE.SphereGeometry(2, 16, 16);
        const markerMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xff4444,
            transparent: true,
            opacity: 0.8
        });
        
        // 示例标记位置
        const poiPositions = [
            { id: 'poi_001', position: new THREE.Vector3(0, -10, 0) },
            { id: 'poi_002', position: new THREE.Vector3(50, 25, 30) },
            { id: 'poi_003', position: new THREE.Vector3(-30, 8, 20) },
            { id: 'poi_004', position: new THREE.Vector3(80, 18, -20) },
            { id: 'poi_005', position: new THREE.Vector3(-60, 3, -40) }
        ];
        
        poiPositions.forEach(poi => {
            const marker = new THREE.Mesh(markerGeometry, markerMaterial.clone());
            marker.position.copy(poi.position);
            marker.userData = { poiId: poi.id };
            
            // 添加标记动画
            marker.userData.originalY = marker.position.y;
            
            this.scene.add(marker);
            this.poiMarkers.set(poi.id, marker);
        });
    }

    bindEvents() {
        // 场景控制按钮事件
        document.getElementById('reset-camera').addEventListener('click', () => {
            this.resetCamera();
        });
        
        document.getElementById('aerial-view').addEventListener('click', () => {
            this.setAerialView();
        });
        
        document.getElementById('ground-view').addEventListener('click', () => {
            this.setGroundView();
        });
        
        document.getElementById('toggle-wireframe').addEventListener('click', () => {
            this.toggleWireframe();
        });
        
        document.getElementById('toggle-lighting').addEventListener('click', () => {
            this.toggleLighting();
        });

        // 鼠标点击事件
        this.renderer.domElement.addEventListener('click', (event) => {
            this.handleClick(event);
        });

        // 设置变更事件
        window.addEventListener('graphicsQualityChanged', (e) => {
            this.updateGraphicsQuality(e.detail.quality);
        });

        window.addEventListener('autoRotateChanged', (e) => {
            this.controls.autoRotate = e.detail.enabled;
        });
    }

    startRenderLoop() {
        const animate = () => {
            if (!this.isPaused) {
                this.animationId = requestAnimationFrame(animate);
                this.update();
                this.render();
            }
        };
        animate();
    }

    update() {
        if (this.stats) this.stats.begin();
        
        this.controls.update();
        
        // 更新水面
        if (this.water) {
            this.water.material.uniforms['time'].value += 1.0 / 60.0;
        }
        
        // 更新POI标记动画
        this.poiMarkers.forEach(marker => {
            marker.position.y = marker.userData.originalY + Math.sin(Date.now() * 0.002) * 2;
        });
        
        if (this.stats) this.stats.end();
    }

    render() {
        this.renderer.render(this.scene, this.camera);
    }

    // 公共方法
    resetCamera() {
        this.camera.position.set(100, 50, 100);
        this.camera.lookAt(0, 0, 0);
        this.controls.reset();
    }

    setAerialView() {
        this.camera.position.set(0, 150, 0);
        this.camera.lookAt(0, 0, 0);
        this.controls.update();
    }

    setGroundView() {
        this.camera.position.set(80, 10, 80);
        this.camera.lookAt(0, 0, 0);
        this.controls.update();
    }

    toggleWireframe() {
        this.settings.enableWireframe = !this.settings.enableWireframe;
        this.models.forEach(model => {
            if (model.material) {
                model.material.wireframe = this.settings.enableWireframe;
            }
        });
    }

    toggleLighting() {
        // 切换光照效果的实现
        this.scene.children.forEach(child => {
            if (child.isLight && child.type !== 'AmbientLight') {
                child.visible = !child.visible;
            }
        });
    }

    handleClick(event) {
        const mouse = new THREE.Vector2();
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / (window.innerHeight - 70)) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);

        const intersects = raycaster.intersectObjects(Array.from(this.poiMarkers.values()));
        
        if (intersects.length > 0) {
            const clickedMarker = intersects[0].object;
            const poiId = clickedMarker.userData.poiId;
            this.onPOIClicked(poiId);
        }
    }

    onPOIClicked(poiId) {
        // 触发POI点击事件
        window.dispatchEvent(new CustomEvent('poiClicked', { 
            detail: { poiId } 
        }));
    }

    updateGraphicsQuality(quality) {
        this.settings.quality = quality;
        
        switch (quality) {
            case 'low':
                this.renderer.setPixelRatio(1);
                this.renderer.shadowMap.enabled = false;
                break;
            case 'medium':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
                this.renderer.shadowMap.enabled = true;
                break;
            case 'high':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                this.renderer.shadowMap.enabled = true;
                break;
        }
    }

    handleResize() {
        if (!this.isInitialized) return;
        
        this.camera.aspect = window.innerWidth / (window.innerHeight - 70);
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight - 70);
    }

    pause() {
        this.isPaused = true;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    resume() {
        this.isPaused = false;
        this.startRenderLoop();
    }

    dispose() {
        this.pause();
        
        // 清理资源
        this.models.forEach(model => {
            if (model.geometry) model.geometry.dispose();
            if (model.material) model.material.dispose();
        });
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        if (this.stats && this.stats.dom.parentNode) {
            this.stats.dom.parentNode.removeChild(this.stats.dom);
        }
    }
}
