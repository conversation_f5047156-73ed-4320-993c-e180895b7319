<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汤山矿坑公园三维导览系统 - 演示页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .feature-card p {
            color: #666;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #555;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .demo-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            transition: border-color 0.3s ease;
        }
        
        .demo-item:hover {
            border-color: #3498db;
        }
        
        .demo-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #bdc3c7;
        }
        
        .demo-placeholder {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            border-radius: 8px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #bdc3c7;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-large {
            padding: 15px 40px;
            font-size: 18px;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        
        .tech-tag {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ready { background: #27ae60; }
        .status-development { background: #f39c12; }
        .status-planned { background: #95a5a6; }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
            
            .feature-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🏞️ 汤山矿坑公园</h1>
            <p>三维AR导览系统演示</p>
        </header>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h3>三维模型展示</h3>
                <p>使用Three.js技术构建的高质量3D场景，真实还原汤山矿坑公园的地形地貌。</p>
                <ul class="feature-list">
                    <li>实时3D渲染</li>
                    <li>交互式场景探索</li>
                    <li>多视角切换</li>
                    <li>景点详细信息</li>
                </ul>
                <div class="status-indicator status-ready"></div>
                <span>已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🗺️</div>
                <h3>智能路线规划</h3>
                <p>提供多种预设路线和自定义路线规划，帮助游客优化游览体验。</p>
                <ul class="feature-list">
                    <li>推荐游览路线</li>
                    <li>自定义路线生成</li>
                    <li>路径可视化</li>
                    <li>时间和距离估算</li>
                </ul>
                <div class="status-indicator status-ready"></div>
                <span>已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>AR增强现实</h3>
                <p>基于WebXR和AR.js的增强现实体验，将虚拟信息叠加到真实环境中。</p>
                <ul class="feature-list">
                    <li>实时AR渲染</li>
                    <li>景点信息叠加</li>
                    <li>导航指引</li>
                    <li>拍照分享功能</li>
                </ul>
                <div class="status-indicator status-ready"></div>
                <span>已完成</span>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🎮</div>
                <h3>AR解密探宝</h3>
                <p>互动式寻宝游戏，通过解密谜题和AR探索增加游览的趣味性。</p>
                <ul class="feature-list">
                    <li>位置感知寻宝</li>
                    <li>解密挑战任务</li>
                    <li>积分奖励系统</li>
                    <li>成就徽章收集</li>
                </ul>
                <div class="status-indicator status-ready"></div>
                <span>已完成</span>
            </div>
        </div>

        <div class="demo-section">
            <h2>🖼️ 功能演示</h2>
            <p style="text-align: center; color: #666; margin-bottom: 20px;">
                以下是系统各个功能模块的界面预览
            </p>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <div class="demo-placeholder">🏞️</div>
                    <h4>3D场景展示</h4>
                    <p>沉浸式三维场景浏览</p>
                </div>
                
                <div class="demo-item">
                    <div class="demo-placeholder">🗺️</div>
                    <h4>路线规划界面</h4>
                    <p>智能路线生成和导航</p>
                </div>
                
                <div class="demo-item">
                    <div class="demo-placeholder">📱</div>
                    <h4>AR导览视图</h4>
                    <p>增强现实信息叠加</p>
                </div>
                
                <div class="demo-item">
                    <div class="demo-placeholder">🎮</div>
                    <h4>探宝游戏界面</h4>
                    <p>互动寻宝和解密挑战</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 技术架构</h2>
            <p style="text-align: center; color: #666; margin-bottom: 20px;">
                基于现代Web技术构建的跨平台解决方案
            </p>
            
            <div class="tech-stack">
                <span class="tech-tag">Three.js</span>
                <span class="tech-tag">WebXR</span>
                <span class="tech-tag">AR.js</span>
                <span class="tech-tag">A-Frame</span>
                <span class="tech-tag">Vite</span>
                <span class="tech-tag">GSAP</span>
                <span class="tech-tag">WebGL</span>
                <span class="tech-tag">PWA</span>
            </div>
            
            <div style="margin-top: 30px; text-align: center;">
                <h3>系统特点</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
                    <div>
                        <strong>🌐 跨平台兼容</strong>
                        <p>支持桌面端和移动端浏览器</p>
                    </div>
                    <div>
                        <strong>⚡ 高性能渲染</strong>
                        <p>优化的3D渲染和AR处理</p>
                    </div>
                    <div>
                        <strong>📱 响应式设计</strong>
                        <p>适配各种屏幕尺寸</p>
                    </div>
                    <div>
                        <strong>🔒 隐私保护</strong>
                        <p>本地数据存储，保护用户隐私</p>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin: 40px 0;">
            <a href="index.html" class="btn btn-large">🚀 启动系统演示</a>
        </div>

        <footer class="footer">
            <p>© 2024 汤山矿坑公园三维导览系统 | 让科技为旅游插上翅膀</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                <small>
                    注意：AR功能需要HTTPS环境和相机权限 | 
                    建议使用Chrome、Safari等现代浏览器
                </small>
            </p>
        </footer>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为演示项添加点击效果
            const demoItems = document.querySelectorAll('.demo-item');
            demoItems.forEach(item => {
                item.addEventListener('click', function() {
                    const title = this.querySelector('h4').textContent;
                    alert(`${title} 功能演示\n\n这个功能已经在主系统中实现，点击"启动系统演示"按钮体验完整功能。`);
                });
            });

            // 添加滚动动画效果
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 为所有卡片添加动画
            const cards = document.querySelectorAll('.feature-card, .demo-section');
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
