// 加载管理器 - 处理应用加载过程和进度显示
export class LoadingManager {
    constructor() {
        this.loadingScreen = document.getElementById('loading-screen');
        this.progressFill = document.getElementById('progress-fill');
        this.loadingText = document.getElementById('loading-text');
        this.currentProgress = 0;
    }

    show() {
        this.loadingScreen.classList.remove('hidden');
        this.updateProgress(0, '正在初始化...');
    }

    hide() {
        this.loadingScreen.style.opacity = '0';
        this.loadingScreen.style.transition = 'opacity 0.5s ease';
        
        setTimeout(() => {
            this.loadingScreen.classList.add('hidden');
            this.loadingScreen.style.opacity = '1';
            this.loadingScreen.style.transition = '';
            
            // 显示主导航
            document.getElementById('main-nav').classList.remove('hidden');
        }, 500);
    }

    updateProgress(progress, text) {
        this.currentProgress = Math.max(this.currentProgress, progress);
        this.progressFill.style.width = `${this.currentProgress}%`;
        
        if (text) {
            this.loadingText.textContent = text;
        }

        // 添加进度动画效果
        if (progress === 100) {
            this.progressFill.style.background = 'linear-gradient(90deg, #2ecc71, #27ae60)';
            this.loadingText.textContent = '加载完成！';
        }
    }

    showError(message) {
        this.progressFill.style.background = 'linear-gradient(90deg, #e74c3c, #c0392b)';
        this.loadingText.textContent = message;
        this.loadingText.style.color = '#e74c3c';
        
        // 添加重试按钮
        const retryBtn = document.createElement('button');
        retryBtn.textContent = '重试';
        retryBtn.className = 'primary-btn';
        retryBtn.style.marginTop = '20px';
        retryBtn.onclick = () => window.location.reload();
        
        const loadingContent = document.querySelector('.loading-content');
        if (!loadingContent.querySelector('button')) {
            loadingContent.appendChild(retryBtn);
        }
    }

    // 预加载资源的方法
    async preloadAssets(assets) {
        const totalAssets = assets.length;
        let loadedAssets = 0;

        const loadPromises = assets.map(asset => {
            return new Promise((resolve, reject) => {
                if (asset.type === 'image') {
                    const img = new Image();
                    img.onload = () => {
                        loadedAssets++;
                        const progress = Math.floor((loadedAssets / totalAssets) * 30) + 10; // 10-40%的进度
                        this.updateProgress(progress, `加载图片资源 ${loadedAssets}/${totalAssets}`);
                        resolve(img);
                    };
                    img.onerror = reject;
                    img.src = asset.url;
                } else if (asset.type === 'model') {
                    // 3D模型加载逻辑将在SceneManager中实现
                    loadedAssets++;
                    const progress = Math.floor((loadedAssets / totalAssets) * 30) + 10;
                    this.updateProgress(progress, `加载3D模型 ${loadedAssets}/${totalAssets}`);
                    resolve();
                } else if (asset.type === 'audio') {
                    const audio = new Audio();
                    audio.oncanplaythrough = () => {
                        loadedAssets++;
                        const progress = Math.floor((loadedAssets / totalAssets) * 30) + 10;
                        this.updateProgress(progress, `加载音频资源 ${loadedAssets}/${totalAssets}`);
                        resolve(audio);
                    };
                    audio.onerror = reject;
                    audio.src = asset.url;
                } else {
                    // 其他类型资源
                    fetch(asset.url)
                        .then(response => response.blob())
                        .then(() => {
                            loadedAssets++;
                            const progress = Math.floor((loadedAssets / totalAssets) * 30) + 10;
                            this.updateProgress(progress, `加载资源 ${loadedAssets}/${totalAssets}`);
                            resolve();
                        })
                        .catch(reject);
                }
            });
        });

        try {
            await Promise.all(loadPromises);
            return true;
        } catch (error) {
            console.error('资源加载失败:', error);
            this.showError('资源加载失败，请检查网络连接');
            return false;
        }
    }
}
